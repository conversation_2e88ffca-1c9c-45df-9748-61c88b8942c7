{"name": "bloom-app-blank-stylesheet", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "dev": "npx convex dev"}, "dependencies": {"@elevenlabs/react": "^0.1.7", "@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/elements": "^2.6.4", "@react-navigation/native": "^7.1.17", "convex": "^1.26.2", "expo": "~53.0.22", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.7", "expo-router": "~5.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.11", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.28.4", "@types/react": "~19.0.14", "eslint": "^9.35.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true, "trustedDependencies": ["unrs-resolver"]}