/* ALIAS Design System */
:root {
  /* ALIAS Design Tokens */
  --alias-background: #000000;
  --alias-foreground: #ffffff;
  --alias-accent: #d7b89c;
  --alias-accent-muted: rgba(215, 184, 156, 0.8);
  --alias-accent-subtle: rgba(215, 184, 156, 0.2);
  --alias-border: rgba(215, 184, 156, 0.05);
  --alias-surface: rgba(0, 0, 0, 0.2);
  --alias-surface-elevated: rgba(0, 0, 0, 0.3);
  --alias-text-muted: rgba(255, 255, 255, 0.7);
  --alias-gradient: radial-gradient(circle at 60% 30%, #0a0a0a 0%, #000000 75%);
  
  /* Base Theme Colors (shadcn/ui) */
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --primary: 0 0% 98%;
  --secondary: 240 3.7% 15.9%;
  --muted: 240 3.7% 15.9%;
  --accent: 240 3.7% 15.9%;
  --destructive: 0 62.8% 30.6%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
  
  /* Sizing */
  --radius: 0.5rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--alias-background);
  color: var(--alias-foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

/* Typography */
h1 {
  font-size: 1.875rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 300;
}

h2 {
  font-size: 1.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 300;
}

h3, h4, h5, h6 {
  font-weight: 300;
  letter-spacing: 0.025em;
}

/* Component Classes */
.alias-section-title {
  color: var(--alias-accent);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 500;
  margin-bottom: 1rem;
}

.alias-section-icon {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--alias-accent-subtle);
  border-radius: var(--radius);
  margin-bottom: 0.5rem;
}

.alias-card {
  background: var(--alias-surface);
  border: 1px solid var(--alias-border);
  border-radius: var(--radius);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
}

.alias-stat-card {
  background: var(--alias-surface);
  border: 1px solid var(--alias-border);
  border-radius: var(--radius);
  padding: 1rem;
  text-align: center;
}

.alias-stat-number {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--alias-accent);
  display: block;
}

.alias-stat-label {
  font-size: 0.75rem;
  color: var(--alias-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.25rem;
}

.alias-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background: var(--alias-accent-subtle);
  color: var(--alias-accent);
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.alias-glass {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid var(--alias-border);
}

.alias-glass-hover {
  transition: all 0.2s ease;
}

.alias-glass-hover:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: var(--alias-accent-subtle);
}

.bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeSlideIn {
  from { 
    opacity: 0; 
    transform: translateY(20px);
  }
  to { 
    opacity: 1; 
    transform: translateY(0);
  }
}

@keyframes growRight {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes rotateSlow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-fadeSlideIn {
  animation: fadeSlideIn 0.6s ease-out;
}

.animate-growRight {
  animation: growRight 1s ease-out;
}

.animate-rotateSlow {
  animation: rotateSlow 20s linear infinite;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

.animation-delay-700 {
  animation-delay: 700ms;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #000000;
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.8) rgba(0, 0, 0, 0.2);
}