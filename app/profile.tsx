import React, { useEffect } from "react";
import { useLocalSearchParams, router } from "expo-router";

export default function ProfileScreen() {
  const { token } = useLocalSearchParams<{ token: string }>();

  useEffect(() => {
    // Redirect to the new intelligence profile screen
    if (token) {
      router.replace(`/intelligence-profile?token=${token}`);
    } else {
      router.replace("/");
    }
  }, [token]);

  return null;
}