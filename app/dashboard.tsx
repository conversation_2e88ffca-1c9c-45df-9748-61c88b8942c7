import React from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useLocalSearchParams, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { Id } from "@/convex/_generated/dataModel";

const { width } = Dimensions.get("window");

interface CollectionCardProps {
  name: string;
  icon: string;
  onPress: () => void;
}

function CollectionCard({ name, icon, onPress }: CollectionCardProps) {
  const getIconName = (iconType: string) => {
    switch (iconType) {
      case "user": return "person-outline";
      case "note": return "document-text-outline";
      case "flash": return "flash-outline";
      default: return "folder-outline";
    }
  };

  return (
    <TouchableOpacity style={styles.collectionCard} onPress={onPress}>
      <View style={styles.collectionIcon}>
        <Ionicons name={getIconName(icon) as any} size={24} color="#ffffff" />
      </View>
      <Text style={styles.collectionLabel}>{name.toUpperCase()}</Text>
    </TouchableOpacity>
  );
}

interface GroupCardProps {
  name: string;
  memberCount: number;
  lastActivity?: number;
  isStarred?: boolean;
  onPress: () => void;
}

function GroupCard({ name, memberCount, lastActivity, isStarred, onPress }: GroupCardProps) {
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return "";
    const date = new Date(timestamp);
    return date.toLocaleDateString("en-US", { 
      month: "short", 
      day: "numeric", 
      year: "numeric" 
    }).toUpperCase();
  };

  return (
    <TouchableOpacity style={styles.groupCard} onPress={onPress}>
      <View style={styles.groupHeader}>
        <View style={styles.groupInfo}>
          {isStarred && (
            <Ionicons name="star" size={16} color="#ffffff" style={styles.starIcon} />
          )}
          <Text style={styles.groupName}>{name}</Text>
        </View>
      </View>
      <View style={styles.groupMeta}>
        <View style={styles.memberInfo}>
          <Text style={styles.memberCount}>{memberCount}</Text>
          <Ionicons name="people-outline" size={14} color="rgba(255, 255, 255, 0.6)" />
        </View>
        {lastActivity && (
          <Text style={styles.lastActivity}>{formatDate(lastActivity)}</Text>
        )}
      </View>
    </TouchableOpacity>
  );
}

export default function DashboardScreen() {
  const { token } = useLocalSearchParams<{ token: string }>();
  const user = useQuery(api.auth.getUserByToken, token ? { token } : "skip");
  const collections = useQuery(api.dashboard.getUserCollections, 
    user ? { userId: user._id } : "skip"
  );
  const groups = useQuery(api.dashboard.getUserGroups, 
    user ? { userId: user._id } : "skip"
  );

  if (!token) {
    router.replace("/");
    return null;
  }

  if (user === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Loading dashboard...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (user === null) {
    router.replace("/");
    return null;
  }

  const handleProfilePress = () => {
    router.push(`/user-profile?token=${token}`);
  };

  const handleCollectionPress = (collectionName: string) => {
    const collectionId = collectionName.toLowerCase();
    router.push(`/collections/${collectionId}?token=${token}`);
  };

  const handleGroupPress = (groupName: string) => {
    // Navigate to group view
    console.log(`Opening ${groupName} group`);
  };

  const handleAddPress = () => {
    // Show add menu
    console.log("Add new item");
  };

  const handleSettingsPress = () => {
    router.push(`/settings?token=${token}`);
  };

  const handleSearchPress = () => {
    router.push(`/search?token=${token}`);
  };

  // Role-based dashboard content
  const getRoleBasedContent = () => {
    switch (user.role) {
      case "super_admin":
      case "admin":
        return {
          title: "Admin Dashboard",
          subtitle: "System Management & Analytics",
          showIntelligenceProfile: false,
        };
      case "dev":
        return {
          title: "Development Hub",
          subtitle: "Projects & Technical Resources",
          showIntelligenceProfile: false,
        };
      case "consultant":
        return {
          title: "Consultant Portal",
          subtitle: "Client Projects & Resources",
          showIntelligenceProfile: false,
        };
      case "stakeholder":
        return {
          title: "Business Intelligence",
          subtitle: "Your AI Transformation Journey",
          showIntelligenceProfile: true,
        };
      case "member":
        return {
          title: "Project Workspace",
          subtitle: "Collaboration & Resources",
          showIntelligenceProfile: false,
        };
      case "customer":
        return {
          title: "AI Toolbox",
          subtitle: "Your Business Automation Suite",
          showIntelligenceProfile: false,
        };
      default:
        return {
          title: "Welcome",
          subtitle: "Explore ALIAS Platform",
          showIntelligenceProfile: false,
        };
    }
  };

  const roleContent = getRoleBasedContent();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <Text style={styles.headerTitle}>{roleContent.title}</Text>
            <TouchableOpacity style={styles.profileButton} onPress={handleProfilePress}>
              <View style={styles.profileAvatar}>
                <Text style={styles.profileInitial}>
                  {user.name.charAt(0).toUpperCase()}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
          <Text style={styles.headerSubtitle}>{roleContent.subtitle}</Text>
        </View>

        {/* Intelligence Profile Quick Access - Only for stakeholders */}
        {roleContent.showIntelligenceProfile && (
          <View style={styles.section}>
            <TouchableOpacity 
              style={styles.intelligenceCard}
              onPress={() => router.push(`/profile?token=${token}`)}
            >
              <View style={styles.intelligenceHeader}>
                <View style={styles.intelligenceIcon}>
                  <Ionicons name="analytics-outline" size={24} color="#ffffff" />
                </View>
                <View style={styles.intelligenceContent}>
                  <Text style={styles.intelligenceTitle}>Your Intelligence Profile</Text>
                  <Text style={styles.intelligenceSubtitle}>
                    View your personalized AI business analysis
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="rgba(255, 255, 255, 0.6)" />
              </View>
            </TouchableOpacity>
          </View>
        )}

        {/* Collections */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Collections</Text>
          <View style={styles.collectionsGrid}>
            {collections?.map((collection) => (
              <CollectionCard
                key={collection._id}
                name={collection.name}
                icon={collection.icon}
                onPress={() => handleCollectionPress(collection.name)}
              />
            )) || (
              <>
                <CollectionCard name="People" icon="user" onPress={() => handleCollectionPress("People")} />
                <CollectionCard name="Notes" icon="note" onPress={() => handleCollectionPress("Notes")} />
                <CollectionCard name="Reminders" icon="flash" onPress={() => handleCollectionPress("Reminders")} />
              </>
            )}
          </View>
        </View>

        {/* Groups */}
        <View style={styles.section}>
          <View style={styles.groupsHeader}>
            <Text style={styles.sectionTitle}>Groups</Text>
            <TouchableOpacity style={styles.addButton} onPress={handleAddPress}>
              <Ionicons name="add" size={20} color="#ffffff" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.groupsList}>
            {groups?.map((group) => (
              <GroupCard
                key={group._id}
                name={group.name}
                memberCount={group.memberCount}
                lastActivity={group.lastActivity}
                isStarred={group.isStarred}
                onPress={() => handleGroupPress(group.name)}
              />
            )) || (
              <>
                <GroupCard
                  name="Starred"
                  memberCount={13}
                  isStarred={true}
                  onPress={() => handleGroupPress("Starred")}
                />
                <GroupCard
                  name="AI Projects"
                  memberCount={8}
                  lastActivity={Date.now() - (2 * 24 * 60 * 60 * 1000)}
                  onPress={() => handleGroupPress("AI Projects")}
                />
              </>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="home" size={24} color="#ffffff" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem} onPress={handleSettingsPress}>
          <Ionicons name="settings-outline" size={24} color="rgba(255, 255, 255, 0.6)" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem} onPress={handleAddPress}>
          <Ionicons name="add" size={24} color="rgba(255, 255, 255, 0.6)" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem} onPress={handleSearchPress}>
          <Ionicons name="search-outline" size={24} color="rgba(255, 255, 255, 0.6)" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="grid-outline" size={24} color="rgba(255, 255, 255, 0.6)" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.7)",
    textAlign: "center",
  },
  header: {
    padding: 24,
    paddingTop: 16,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: "600",
    color: "#ffffff",
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  profileButton: {
    padding: 4,
  },
  profileAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    alignItems: "center",
    justifyContent: "center",
  },
  profileInitial: {
    fontSize: 16,
    fontWeight: "600",
    color: "#ffffff",
  },
  section: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 20,
  },
  intelligenceCard: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 16,
    padding: 20,
  },
  intelligenceHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  intelligenceIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  intelligenceContent: {
    flex: 1,
  },
  intelligenceTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 4,
  },
  intelligenceSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.7)",
  },
  collectionsGrid: {
    flexDirection: "row",
    gap: 16,
  },
  collectionCard: {
    flex: 1,
    aspectRatio: 1,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    gap: 12,
  },
  collectionIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  collectionLabel: {
    fontSize: 12,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.8)",
    letterSpacing: 1,
    textAlign: "center",
  },
  groupsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  groupsList: {
    gap: 12,
  },
  groupCard: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 16,
    padding: 20,
  },
  groupHeader: {
    marginBottom: 12,
  },
  groupInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  starIcon: {
    marginRight: 8,
  },
  groupName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
  },
  groupMeta: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  memberInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  memberCount: {
    fontSize: 14,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.8)",
  },
  lastActivity: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.6)",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  bottomNav: {
    flexDirection: "row",
    backgroundColor: "rgba(0, 0, 0, 0.9)",
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.1)",
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  navItem: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 8,
  },
});