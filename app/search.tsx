import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

interface SearchResultProps {
  type: "person" | "note" | "group" | "project";
  title: string;
  subtitle: string;
  onPress: () => void;
}

function SearchResult({ type, title, subtitle, onPress }: SearchResultProps) {
  const getIcon = () => {
    switch (type) {
      case "person": return "person-outline";
      case "note": return "document-text-outline";
      case "group": return "people-outline";
      case "project": return "briefcase-outline";
      default: return "search-outline";
    }
  };

  const getTypeColor = () => {
    switch (type) {
      case "person": return "#3b82f6";
      case "note": return "#10b981";
      case "group": return "#f59e0b";
      case "project": return "#8b5cf6";
      default: return "#6b7280";
    }
  };

  return (
    <TouchableOpacity style={styles.resultItem} onPress={onPress}>
      <View style={[styles.resultIcon, { backgroundColor: getTypeColor() + "20" }]}>
        <Ionicons name={getIcon() as any} size={20} color={getTypeColor()} />
      </View>
      <View style={styles.resultContent}>
        <Text style={styles.resultTitle}>{title}</Text>
        <Text style={styles.resultSubtitle}>{subtitle}</Text>
      </View>
      <View style={styles.resultType}>
        <Text style={[styles.resultTypeText, { color: getTypeColor() }]}>
          {type.toUpperCase()}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

interface QuickActionProps {
  icon: string;
  title: string;
  subtitle: string;
  onPress: () => void;
}

function QuickAction({ icon, title, subtitle, onPress }: QuickActionProps) {
  return (
    <TouchableOpacity style={styles.quickAction} onPress={onPress}>
      <View style={styles.quickActionIcon}>
        <Ionicons name={icon as any} size={24} color="#d7b89c" />
      </View>
      <View style={styles.quickActionContent}>
        <Text style={styles.quickActionTitle}>{title}</Text>
        <Text style={styles.quickActionSubtitle}>{subtitle}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="rgba(255, 255, 255, 0.4)" />
    </TouchableOpacity>
  );
}

export default function SearchScreen() {
  const { token } = useLocalSearchParams<{ token: string }>();
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  const handleBack = () => {
    router.back();
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.length > 0) {
      setIsSearching(true);
      // Simulate search delay
      setTimeout(() => {
        setIsSearching(false);
      }, 500);
    }
  };

  const mockResults = [
    {
      type: "person" as const,
      title: "Arlene Jayne Bate",
      subtitle: "Founder at Mrs. Muscle Cleaning",
    },
    {
      type: "note" as const,
      title: "Client Meeting Notes",
      subtitle: "Discussed AI automation opportunities...",
    },
    {
      type: "group" as const,
      title: "AI Projects",
      subtitle: "8 members • Last activity 2 days ago",
    },
    {
      type: "project" as const,
      title: "Mrs. Muscle Automation",
      subtitle: "Active • Due in 3 weeks",
    },
  ];

  const filteredResults = searchQuery.length > 0 
    ? mockResults.filter(result => 
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.subtitle.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="chevron-back" size={24} color="#ffffff" />
        </TouchableOpacity>
        <View style={styles.searchContainer}>
          <Ionicons name="search-outline" size={20} color="rgba(255, 255, 255, 0.6)" />
          <TextInput
            style={styles.searchInput}
            value={searchQuery}
            onChangeText={handleSearch}
            placeholder="Search people, notes, groups..."
            placeholderTextColor="rgba(255, 255, 255, 0.4)"
            autoFocus={true}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => handleSearch("")}>
              <Ionicons name="close-circle" size={20} color="rgba(255, 255, 255, 0.6)" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {searchQuery.length === 0 ? (
          // Quick Actions when no search
          <View style={styles.quickActionsSection}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.quickActionsList}>
              <QuickAction
                icon="person-add-outline"
                title="Add Person"
                subtitle="Create a new contact"
                onPress={() => console.log("Add person")}
              />
              <QuickAction
                icon="document-text-outline"
                title="New Note"
                subtitle="Create a new note"
                onPress={() => console.log("New note")}
              />
              <QuickAction
                icon="people-outline"
                title="Create Group"
                subtitle="Start a new group"
                onPress={() => console.log("Create group")}
              />
              <QuickAction
                icon="briefcase-outline"
                title="New Project"
                subtitle="Start a new project"
                onPress={() => console.log("New project")}
              />
            </View>

            <Text style={styles.sectionTitle}>Recent Searches</Text>
            <View style={styles.recentSearches}>
              <TouchableOpacity style={styles.recentSearchItem}>
                <Ionicons name="time-outline" size={16} color="rgba(255, 255, 255, 0.6)" />
                <Text style={styles.recentSearchText}>Mrs. Muscle</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.recentSearchItem}>
                <Ionicons name="time-outline" size={16} color="rgba(255, 255, 255, 0.6)" />
                <Text style={styles.recentSearchText}>AI automation</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.recentSearchItem}>
                <Ionicons name="time-outline" size={16} color="rgba(255, 255, 255, 0.6)" />
                <Text style={styles.recentSearchText}>TechStartup</Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          // Search Results
          <View style={styles.resultsSection}>
            {isSearching ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#d7b89c" />
                <Text style={styles.loadingText}>Searching...</Text>
              </View>
            ) : (
              <>
                <Text style={styles.sectionTitle}>
                  {filteredResults.length} result{filteredResults.length !== 1 ? 's' : ''} for "{searchQuery}"
                </Text>
                <View style={styles.resultsList}>
                  {filteredResults.map((result, index) => (
                    <SearchResult
                      key={index}
                      type={result.type}
                      title={result.title}
                      subtitle={result.subtitle}
                      onPress={() => console.log(`Open ${result.title}`)}
                    />
                  ))}
                </View>
                {filteredResults.length === 0 && (
                  <View style={styles.noResults}>
                    <Ionicons name="search-outline" size={48} color="rgba(255, 255, 255, 0.3)" />
                    <Text style={styles.noResultsTitle}>No results found</Text>
                    <Text style={styles.noResultsText}>
                      Try searching for people, notes, groups, or projects
                    </Text>
                  </View>
                )}
              </>
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)",
    gap: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  searchContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    paddingHorizontal: 16,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: "#ffffff",
  },
  scrollView: {
    flex: 1,
  },
  quickActionsSection: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 16,
  },
  quickActionsList: {
    gap: 8,
    marginBottom: 32,
  },
  quickAction: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 16,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: "rgba(215, 184, 156, 0.1)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  quickActionContent: {
    flex: 1,
  },
  quickActionTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#ffffff",
    marginBottom: 2,
  },
  quickActionSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
  },
  recentSearches: {
    gap: 8,
  },
  recentSearchItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 8,
    gap: 12,
  },
  recentSearchText: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
  },
  resultsSection: {
    padding: 24,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 48,
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.7)",
  },
  resultsList: {
    gap: 8,
  },
  resultItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 16,
  },
  resultIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#ffffff",
    marginBottom: 2,
  },
  resultSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
  },
  resultType: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 6,
  },
  resultTypeText: {
    fontSize: 10,
    fontWeight: "600",
    letterSpacing: 0.5,
  },
  noResults: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 48,
    gap: 16,
  },
  noResultsTitle: {
    fontSize: 18,
    fontWeight: "500",
    color: "#ffffff",
  },
  noResultsText: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
    textAlign: "center",
    lineHeight: 20,
  },
});