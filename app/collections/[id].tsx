import React from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

export default function CollectionDetailScreen() {
  const { id, token } = useLocalSearchParams<{ id: string; token: string }>();

  const handleBack = () => {
    router.back();
  };

  const handleAdd = () => {
    console.log("Add item to collection");
  };

  const getCollectionData = (collectionId: string) => {
    switch (collectionId) {
      case "people":
        return {
          name: "People",
          icon: "person-outline",
          items: [
            { id: "1", name: "<PERSON><PERSON>", role: "Founder", company: "Mrs. Muscle Cleaning", avatar: "https://scontent-syd2-1.xx.fbcdn.net/v/t39.30808-6/430335013_823322196210485_4341867507489197989_n.jpg" },
            { id: "2", name: "<PERSON>", role: "CEO", company: "TechStartup Inc.", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face" },
            { id: "3", name: "Sarah Johnson", role: "CTO", company: "InnovateCorp", avatar: null },
          ]
        };
      case "notes":
        return {
          name: "Notes",
          icon: "document-text-outline",
          items: [
            { id: "1", title: "Client Meeting Notes", content: "Discussed AI automation opportunities...", date: "2024-01-08" },
            { id: "2", title: "Project Requirements", content: "Key features for the new platform...", date: "2024-01-07" },
            { id: "3", title: "Market Research", content: "Industry trends and competitive analysis...", date: "2024-01-06" },
          ]
        };
      case "reminders":
        return {
          name: "Reminders",
          icon: "flash-outline",
          items: [
            { id: "1", title: "Follow up with Mrs. Muscle", time: "Tomorrow 10:00 AM", priority: "high" },
            { id: "2", title: "Review TechStartup proposal", time: "Friday 2:00 PM", priority: "medium" },
            { id: "3", title: "Team standup meeting", time: "Daily 9:00 AM", priority: "low" },
          ]
        };
      default:
        return { name: "Collection", icon: "folder-outline", items: [] };
    }
  };

  const collection = getCollectionData(id);

  const renderPersonItem = (person: any) => (
    <TouchableOpacity key={person.id} style={styles.personItem}>
      <View style={styles.personAvatar}>
        {person.avatar ? (
          <View style={styles.avatarImage} />
        ) : (
          <Text style={styles.avatarInitial}>{person.name.charAt(0)}</Text>
        )}
      </View>
      <View style={styles.personInfo}>
        <Text style={styles.personName}>{person.name}</Text>
        <Text style={styles.personRole}>{person.role} at {person.company}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="rgba(255, 255, 255, 0.4)" />
    </TouchableOpacity>
  );

  const renderNoteItem = (note: any) => (
    <TouchableOpacity key={note.id} style={styles.noteItem}>
      <View style={styles.noteContent}>
        <Text style={styles.noteTitle}>{note.title}</Text>
        <Text style={styles.noteText}>{note.content}</Text>
        <Text style={styles.noteDate}>{note.date}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="rgba(255, 255, 255, 0.4)" />
    </TouchableOpacity>
  );

  const renderReminderItem = (reminder: any) => (
    <TouchableOpacity key={reminder.id} style={styles.reminderItem}>
      <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(reminder.priority) }]} />
      <View style={styles.reminderContent}>
        <Text style={styles.reminderTitle}>{reminder.title}</Text>
        <Text style={styles.reminderTime}>{reminder.time}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="rgba(255, 255, 255, 0.4)" />
    </TouchableOpacity>
  );

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "#ef4444";
      case "medium": return "#f59e0b";
      case "low": return "#10b981";
      default: return "#6b7280";
    }
  };

  const renderItems = () => {
    switch (id) {
      case "people":
        return collection.items.map(renderPersonItem);
      case "notes":
        return collection.items.map(renderNoteItem);
      case "reminders":
        return collection.items.map(renderReminderItem);
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="chevron-back" size={24} color="#ffffff" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>{collection.name}</Text>
          <Text style={styles.headerSubtitle}>{collection.items.length} items</Text>
        </View>
        <TouchableOpacity style={styles.addButton} onPress={handleAdd}>
          <Ionicons name="add" size={24} color="#d7b89c" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.itemsList}>
          {renderItems()}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)",
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  headerContent: {
    flex: 1,
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
  },
  headerSubtitle: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.6)",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(215, 184, 156, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  scrollView: {
    flex: 1,
  },
  itemsList: {
    padding: 24,
    gap: 12,
  },
  personItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 16,
  },
  personAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(215, 184, 156, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  avatarImage: {
    width: "100%",
    height: "100%",
    borderRadius: 24,
    backgroundColor: "rgba(215, 184, 156, 0.4)",
  },
  avatarInitial: {
    fontSize: 18,
    fontWeight: "600",
    color: "#d7b89c",
  },
  personInfo: {
    flex: 1,
  },
  personName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 4,
  },
  personRole: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.7)",
  },
  noteItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 16,
  },
  noteContent: {
    flex: 1,
  },
  noteTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 4,
  },
  noteText: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.7)",
    marginBottom: 8,
  },
  noteDate: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.5)",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  reminderItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 16,
  },
  priorityIndicator: {
    width: 4,
    height: 32,
    borderRadius: 2,
    marginRight: 16,
  },
  reminderContent: {
    flex: 1,
  },
  reminderTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 4,
  },
  reminderTime: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.7)",
  },
});