import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  StatusBar,
} from "react-native";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

const { width } = Dimensions.get("window");

export default function LoginScreen() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberDevice, setRememberDevice] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showEmailInput, setShowEmailInput] = useState(false);
  
  const createSession = useMutation(api.auth.createSession);
  const createDevSession = useMutation(api.auth.createDevSession);
  const createSampleProfile = useMutation(api.admin.createSampleProfile);
  const createMrsMuscleProfile = useMutation(api.admin.createMrsMuscleProfile);
  const createAdminUser = useMutation(api.admin.createAdminUser);
  const createDevBypass = useMutation(api.admin.createDevBypass);
  const fixUsersWithoutRoles = useMutation(api.admin.fixUsersWithoutRoles);
  const cleanupDatabase = useMutation(api.fixDatabase.cleanupDatabase);

  const handleEmailLogin = async () => {
    if (!email.trim()) {
      Alert.alert("Error", "Please enter your email address");
      return;
    }

    if (!email.includes("@")) {
      Alert.alert("Error", "Please enter a valid email address");
      return;
    }

    setLoading(true);
    try {
      const session = await createSession({ email: email.toLowerCase().trim() });
      
      if (session.user.role === "stakeholder") {
        router.replace(`/intelligence-profile?token=${session.token}`);
      } else {
        router.replace(`/dashboard?token=${session.token}`);
      }
    } catch (error) {
      console.error("Login failed:", error);
      Alert.alert("Access Denied", "Your intelligence profile is not yet available. Please contact ALIAS for access.");
    } finally {
      setLoading(false);
    }
  };

  const handleDevBypass = async () => {
    setLoading(true);
    try {
      // Clean up database first
      await cleanupDatabase({});
      
      // Fix any users without roles
      await fixUsersWithoutRoles({});
      
      await createDevBypass({});
      const session = await createDevSession({ email: "<EMAIL>" });
      router.replace(`/dashboard?token=${session.token}`);
    } catch (error) {
      console.error("Dev bypass failed:", error);
      Alert.alert("Error", "Failed to create dev session");
    } finally {
      setLoading(false);
    }
  };

  const handleDemoAccess = async (demoType: string) => {
    setLoading(true);
    try {
      // Clean up database first
      await cleanupDatabase({});
      
      // Fix any users without roles
      await fixUsersWithoutRoles({});
      
      let session;
      
      switch (demoType) {
        case "admin":
          await createAdminUser({});
          session = await createSession({ email: "<EMAIL>" });
          router.replace(`/dashboard?token=${session.token}`);
          break;
        case "techstartup":
          await createSampleProfile({});
          session = await createSession({ email: "<EMAIL>" });
          router.replace(`/intelligence-profile?token=${session.token}`);
          break;
        case "mrsmuscle":
          await createMrsMuscleProfile({});
          session = await createSession({ email: "<EMAIL>" });
          router.replace(`/intelligence-profile?token=${session.token}`);
          break;
      }
    } catch (error) {
      console.error("Demo access failed:", error);
      Alert.alert("Error", "Failed to create demo profile");
    } finally {
      setLoading(false);
    }
  };

  if (showEmailInput) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#000000" />
        <KeyboardAvoidingView 
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.keyboardView}
        >
          <View style={styles.content}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => setShowEmailInput(false)}
            >
              <Ionicons name="chevron-back" size={24} color="#ffffff" />
            </TouchableOpacity>

            <View style={styles.centerContent}>
              {/* Login Portal Card */}
              <View style={styles.loginCard}>
                {/* Header with gradient background */}
                <View style={styles.cardHeader}>
                  <View style={styles.secureAccessBadge}>
                    <Text style={styles.secureAccessText}>SECURE ACCESS</Text>
                  </View>
                  <Text style={styles.loginPortalTitle}>Login Portal</Text>
                  <View style={styles.headerLine} />
                </View>

                {/* Auth Section */}
                <View style={styles.authSection}>
                  <View style={styles.authBadge}>
                    <Text style={styles.authBadgeText}>AUTHENTICATION</Text>
                  </View>
                  <Text style={styles.accountLoginTitle}>Account Login</Text>

                  <View style={styles.form}>
                    <View style={styles.inputGroup}>
                      <Text style={styles.inputLabel}>EMAIL</Text>
                      <TextInput
                        style={styles.input}
                        value={email}
                        onChangeText={setEmail}
                        placeholder="<EMAIL>"
                        placeholderTextColor="rgba(255, 255, 255, 0.4)"
                        keyboardType="email-address"
                        autoCapitalize="none"
                        autoCorrect={false}
                        autoFocus={true}
                        editable={!loading}
                      />
                    </View>

                    <View style={styles.inputGroup}>
                      <View style={styles.passwordHeader}>
                        <Text style={styles.inputLabel}>PASSWORD</Text>
                        <TouchableOpacity>
                          <Text style={styles.forgotLink}>Forgot?</Text>
                        </TouchableOpacity>
                      </View>
                      <TextInput
                        style={styles.input}
                        value={password}
                        onChangeText={setPassword}
                        placeholder="••••••••"
                        placeholderTextColor="rgba(255, 255, 255, 0.4)"
                        secureTextEntry
                        editable={!loading}
                      />
                    </View>

                    <View style={styles.checkboxContainer}>
                      <TouchableOpacity 
                        style={styles.checkbox}
                        onPress={() => setRememberDevice(!rememberDevice)}
                      >
                        {rememberDevice && (
                          <Ionicons name="checkmark" size={12} color="#ffffff" />
                        )}
                      </TouchableOpacity>
                      <Text style={styles.checkboxLabel}>Remember this device</Text>
                    </View>

                    <View style={styles.buttonGroup}>
                      <TouchableOpacity
                        style={[styles.loginButton, loading && styles.buttonDisabled]}
                        onPress={handleEmailLogin}
                        disabled={loading}
                      >
                        {loading ? (
                          <ActivityIndicator color="#ffffff" size="small" />
                        ) : (
                          <>
                            <Ionicons name="log-in-outline" size={16} color="#ffffff" />
                            <Text style={styles.loginButtonText}>Login</Text>
                          </>
                        )}
                      </TouchableOpacity>
                      
                      <TouchableOpacity style={styles.ssoButton}>
                        <Ionicons name="shield-outline" size={16} color="rgba(255, 255, 255, 0.7)" />
                        <Text style={styles.ssoButtonText}>SSO</Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {/* Footer */}
                  <View style={styles.cardFooter}>
                    <View style={styles.dividerGradient} />
                    <Text style={styles.footerText}>
                      Don't have an account? <Text style={styles.footerLink}>Request Access</Text>
                    </Text>
                    <View style={styles.statusIndicator}>
                      <View style={styles.statusDot} />
                      <Text style={styles.statusText}>System Status: Operational</Text>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      <View style={styles.content}>
        <View style={styles.centerContent}>
          {/* Logo */}
          <View style={styles.logoContainer}>
            <Text style={styles.logo}>ALIAS</Text>
          </View>

          {/* Header Text */}
          <View style={styles.headerText}>
            <Text style={styles.title}>Welcome</Text>
            <Text style={styles.subtitle}>Access your AI Intelligence Profile</Text>
          </View>

          {/* Auth Options */}
          <View style={styles.authOptions}>
            {/* Email Button */}
            <TouchableOpacity
              style={styles.emailButton}
              onPress={() => setShowEmailInput(true)}
              disabled={loading}
            >
              <Text style={styles.emailButtonText}>Continue with email</Text>
            </TouchableOpacity>

            {/* Divider */}
            <View style={styles.divider} />

            {/* Demo Access */}
            <View style={styles.demoSection}>
              <Text style={styles.demoTitle}>Demo Intelligence Profiles</Text>
              <View style={styles.demoButtons}>
                <TouchableOpacity
                  style={styles.demoButton}
                  onPress={() => handleDemoAccess("mrsmuscle")}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator color="rgba(255, 255, 255, 0.8)" size="small" />
                  ) : (
                    <Text style={styles.demoButtonText}>Mrs. Muscle Cleaning</Text>
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.demoButton}
                  onPress={() => handleDemoAccess("techstartup")}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator color="rgba(255, 255, 255, 0.8)" size="small" />
                  ) : (
                    <Text style={styles.demoButtonText}>TechStartup Inc.</Text>
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.demoButtonSecondary}
                  onPress={() => handleDemoAccess("admin")}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator color="rgba(255, 255, 255, 0.6)" size="small" />
                  ) : (
                    <Text style={styles.demoButtonSecondaryText}>Admin Dashboard</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>

            {/* Dev Bypass - Always show for easy access */}
            <View style={styles.devSection}>
              <Text style={styles.devTitle}>Development</Text>
              <TouchableOpacity
                style={styles.devButton}
                onPress={handleDevBypass}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="rgba(255, 255, 255, 0.4)" size="small" />
                ) : (
                  <Text style={styles.devButtonText}>Dev Bypass</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Powered by ALIAS AI Intelligence Platform
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: "space-between",
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 16,
    alignSelf: "flex-start",
  },
  centerContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    maxWidth: 400,
    alignSelf: "center",
    width: "100%",
  },
  
  // Login Portal Card Design
  loginCard: {
    width: "100%",
    backgroundColor: "#171717",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  cardHeader: {
    height: 150,
    backgroundColor: "#171717",
    padding: 16,
    justifyContent: "flex-start",
    position: "relative",
  },
  secureAccessBadge: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
    marginBottom: 8,
  },
  secureAccessText: {
    fontSize: 10,
    color: "rgba(255, 255, 255, 0.6)",
    fontWeight: "500",
    letterSpacing: 1,
  },
  loginPortalTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: "#ffffff",
    marginBottom: 8,
  },
  headerLine: {
    width: 48,
    height: 4,
    backgroundColor: "rgba(255, 255, 255, 0.6)",
    borderRadius: 2,
  },
  authSection: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    padding: 24,
  },
  authBadge: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
    marginBottom: 8,
  },
  authBadgeText: {
    fontSize: 10,
    color: "rgba(255, 255, 255, 0.6)",
    fontWeight: "500",
    letterSpacing: 1,
  },
  accountLoginTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "rgba(255, 255, 255, 0.9)",
    marginBottom: 16,
  },
  form: {
    gap: 16,
  },
  inputGroup: {
    gap: 4,
  },
  passwordHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  inputLabel: {
    fontSize: 10,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.7)",
    letterSpacing: 1,
    textTransform: "uppercase",
  },
  forgotLink: {
    fontSize: 10,
    color: "rgba(255, 255, 255, 0.4)",
    textTransform: "uppercase",
  },
  input: {
    height: 40,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.9)",
  },
  checkboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  checkbox: {
    width: 16,
    height: 16,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 2,
    alignItems: "center",
    justifyContent: "center",
  },
  checkboxLabel: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.4)",
  },
  buttonGroup: {
    flexDirection: "row",
    gap: 12,
  },
  loginButton: {
    flex: 1,
    height: 40,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  loginButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.9)",
  },
  ssoButton: {
    flex: 1,
    height: 40,
    backgroundColor: "#000000",
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  ssoButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.7)",
  },
  cardFooter: {
    paddingTop: 16,
    paddingHorizontal: 24,
    paddingBottom: 24,
    alignItems: "center",
    gap: 16,
  },
  dividerGradient: {
    height: 1,
    width: "100%",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  statusIndicator: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#10b981",
  },
  statusText: {
    fontSize: 10,
    color: "rgba(255, 255, 255, 0.6)",
  },
  
  // Original Design Elements
  logoContainer: {
    width: 64,
    height: 64,
    borderRadius: 12,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 32,
  },
  logo: {
    fontSize: 16,
    fontWeight: "600",
    color: "#ffffff",
    letterSpacing: 2,
  },
  headerText: {
    alignItems: "center",
    marginBottom: 48,
  },
  title: {
    fontSize: 28,
    fontWeight: "600",
    color: "#ffffff",
    textAlign: "center",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.7)",
    textAlign: "center",
  },
  authOptions: {
    width: "100%",
    gap: 24,
  },
  divider: {
    height: 1,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    width: "100%",
  },
  emailButton: {
    height: 48,
    backgroundColor: "#ffffff",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  emailButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000000",
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  demoSection: {
    width: "100%",
  },
  demoTitle: {
    fontSize: 12,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.6)",
    letterSpacing: 1,
    textTransform: "uppercase",
    textAlign: "center",
    marginBottom: 16,
  },
  demoButtons: {
    gap: 8,
  },
  demoButton: {
    height: 40,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 6,
    alignItems: "center",
    justifyContent: "center",
  },
  demoButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.8)",
  },
  demoButtonSecondary: {
    height: 40,
    backgroundColor: "rgba(255, 255, 255, 0.02)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 6,
    alignItems: "center",
    justifyContent: "center",
  },
  demoButtonSecondaryText: {
    fontSize: 14,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.6)",
  },
  devSection: {
    width: "100%",
    marginTop: 16,
  },
  devTitle: {
    fontSize: 12,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.4)",
    letterSpacing: 1,
    textTransform: "uppercase",
    textAlign: "center",
    marginBottom: 8,
  },
  devButton: {
    height: 32,
    backgroundColor: "rgba(255, 255, 255, 0.02)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 4,
    alignItems: "center",
    justifyContent: "center",
  },
  devButtonText: {
    fontSize: 12,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.4)",
  },
  footer: {
    alignItems: "center",
    paddingBottom: 24,
  },
  footerText: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.4)",
    textAlign: "center",
    letterSpacing: 0.5,
  },
  footerLink: {
    color: "rgba(255, 255, 255, 0.7)",
  },
});