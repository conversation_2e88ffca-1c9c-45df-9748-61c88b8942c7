import React from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

const { width } = Dimensions.get("window");

interface SolutionCardProps {
  title: string;
  description: string;
  category: string;
  impact: string;
  timeframe: string;
  investment: string;
  onPress: () => void;
}

function SolutionCard({ title, description, category, impact, timeframe, investment, onPress }: SolutionCardProps) {
  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case "automation": return "#3b82f6";
      case "analytics": return "#10b981";
      case "communication": return "#f59e0b";
      case "integration": return "#8b5cf6";
      case "optimization": return "#ef4444";
      default: return "#6b7280";
    }
  };

  return (
    <TouchableOpacity style={styles.solutionCard} onPress={onPress}>
      <View style={styles.cardHeader}>
        <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(category) + "20" }]}>
          <Text style={[styles.categoryText, { color: getCategoryColor(category) }]}>
            {category.toUpperCase()}
          </Text>
        </View>
        <View style={styles.impactBadge}>
          <Text style={styles.impactText}>{impact}</Text>
        </View>
      </View>
      
      <Text style={styles.cardTitle}>{title}</Text>
      <Text style={styles.cardDescription}>{description}</Text>
      
      <View style={styles.cardFooter}>
        <View style={styles.metaItem}>
          <Ionicons name="time-outline" size={14} color="rgba(255, 255, 255, 0.6)" />
          <Text style={styles.metaText}>{timeframe}</Text>
        </View>
        <View style={styles.metaItem}>
          <Ionicons name="trending-up-outline" size={14} color="rgba(255, 255, 255, 0.6)" />
          <Text style={styles.metaText}>{investment}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

interface CollectionCardProps {
  title: string;
  count: number;
  color: string;
  onPress: () => void;
}

function CollectionCard({ title, count, color, onPress }: CollectionCardProps) {
  return (
    <TouchableOpacity style={styles.collectionCard} onPress={onPress}>
      <View style={[styles.collectionIcon, { backgroundColor: color + "20" }]}>
        <View style={[styles.collectionDot, { backgroundColor: color }]} />
      </View>
      <Text style={styles.collectionTitle}>{title}</Text>
      <Text style={styles.collectionCount}>{count} solutions</Text>
    </TouchableOpacity>
  );
}

export default function SolutionsScreen() {
  const { token } = useLocalSearchParams<{ token: string }>();

  const handleBack = () => {
    router.back();
  };

  const handleSolutionPress = (solutionTitle: string) => {
    console.log(`Opening solution: ${solutionTitle}`);
  };

  const handleCollectionPress = (collectionTitle: string) => {
    console.log(`Opening collection: ${collectionTitle}`);
  };

  const solutions = [
    {
      title: "Automated Customer Onboarding",
      description: "AI-powered chatbots and workflows to streamline customer onboarding, reducing manual effort by 70%.",
      category: "Automation",
      impact: "High",
      timeframe: "2-4 weeks",
      investment: "$25K - $50K"
    },
    {
      title: "Predictive Analytics Suite",
      description: "Advanced analytics to predict customer behavior, market trends, and operational bottlenecks.",
      category: "Analytics",
      impact: "High",
      timeframe: "6-8 weeks",
      investment: "$100K - $200K"
    },
    {
      title: "Smart Communication Hub",
      description: "Unified communication platform with AI-powered routing and automated responses.",
      category: "Communication",
      impact: "Medium",
      timeframe: "4-6 weeks",
      investment: "$40K - $80K"
    },
    {
      title: "Intelligent Lead Scoring",
      description: "Machine learning algorithms to automatically score and prioritize leads based on conversion probability.",
      category: "Analytics",
      impact: "Medium",
      timeframe: "3-5 weeks",
      investment: "$30K - $60K"
    },
    {
      title: "Process Optimization Engine",
      description: "AI-driven analysis and optimization of business processes to eliminate bottlenecks and improve efficiency.",
      category: "Optimization",
      impact: "High",
      timeframe: "8-12 weeks",
      investment: "$150K - $300K"
    },
    {
      title: "Integration Platform",
      description: "Seamless integration of existing tools and systems with AI-powered data synchronization.",
      category: "Integration",
      impact: "Medium",
      timeframe: "4-8 weeks",
      investment: "$60K - $120K"
    }
  ];

  const collections = [
    { title: "Quick Wins", count: 12, color: "#10b981" },
    { title: "Strategic", count: 8, color: "#3b82f6" },
    { title: "Automation", count: 15, color: "#f59e0b" },
    { title: "Analytics", count: 10, color: "#8b5cf6" }
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="chevron-back" size={24} color="#ffffff" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Solutions Gallery</Text>
          <Text style={styles.headerSubtitle}>AI-Powered Business Solutions</Text>
        </View>
        <TouchableOpacity style={styles.searchButton}>
          <Ionicons name="search-outline" size={24} color="#ffffff" />
        </TouchableOpacity>
      </View>

      {/* Main Content */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Featured Collections */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Collections</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View all</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.collectionsContainer}
          >
            {collections.map((collection, index) => (
              <CollectionCard
                key={index}
                title={collection.title}
                count={collection.count}
                color={collection.color}
                onPress={() => handleCollectionPress(collection.title)}
              />
            ))}
          </ScrollView>
        </View>

        {/* Solutions Grid */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Explore Solutions</Text>
            <View style={styles.filterToggle}>
              <TouchableOpacity style={styles.filterActive}>
                <Text style={styles.filterActiveText}>Recent</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.filterInactive}>
                <Text style={styles.filterInactiveText}>Popular</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={styles.solutionsGrid}>
            {solutions.map((solution, index) => (
              <SolutionCard
                key={index}
                title={solution.title}
                description={solution.description}
                category={solution.category}
                impact={solution.impact}
                timeframe={solution.timeframe}
                investment={solution.investment}
                onPress={() => handleSolutionPress(solution.title)}
              />
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)",
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  headerContent: {
    flex: 1,
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
  },
  headerSubtitle: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.6)",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
  },
  viewAllText: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.6)",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  filterToggle: {
    flexDirection: "row",
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 20,
    padding: 2,
  },
  filterActive: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 18,
  },
  filterActiveText: {
    fontSize: 10,
    color: "#ffffff",
    fontWeight: "500",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  filterInactive: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  filterInactiveText: {
    fontSize: 10,
    color: "rgba(255, 255, 255, 0.6)",
    fontWeight: "500",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  collectionsContainer: {
    paddingRight: 24,
    gap: 12,
  },
  collectionCard: {
    width: 120,
    alignItems: "center",
  },
  collectionIcon: {
    width: 60,
    height: 60,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  collectionDot: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  collectionTitle: {
    fontSize: 12,
    fontWeight: "500",
    color: "#ffffff",
    textAlign: "center",
    marginBottom: 2,
  },
  collectionCount: {
    fontSize: 10,
    color: "rgba(255, 255, 255, 0.6)",
    textAlign: "center",
  },
  solutionsGrid: {
    gap: 12,
  },
  solutionCard: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 16,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  categoryText: {
    fontSize: 10,
    fontWeight: "600",
    letterSpacing: 0.5,
  },
  impactBadge: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  impactText: {
    fontSize: 10,
    fontWeight: "600",
    color: "#ffffff",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
    lineHeight: 20,
    marginBottom: 16,
  },
  cardFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  metaItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.6)",
  },
});