import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Dimensions,
  Image,
  Platform,
  StatusBar,
} from "react-native";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useLocalSearchParams, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";

const { width } = Dimensions.get("window");

interface MetricCardProps {
  label: string;
  value: string;
  trend?: string;
}

function MetricCard({ label, value, trend }: MetricCardProps) {
  return (
    <View style={styles.metricCard}>
      <Text style={styles.metricValue}>{value}</Text>
      <Text style={styles.metricLabel}>{label}</Text>
      {trend && <Text style={styles.metricTrend}>{trend}</Text>}
    </View>
  );
}

interface OpportunityCardProps {
  title: string;
  description: string;
  impact: string;
  timeframe: string;
}

function OpportunityCard({ title, description, impact, timeframe }: OpportunityCardProps) {
  const getImpactColor = (impact: string) => {
    switch (impact.toLowerCase()) {
      case "high": return "#ffffff";
      case "medium": return "rgba(255, 255, 255, 0.8)";
      case "low": return "rgba(255, 255, 255, 0.6)";
      default: return "#ffffff";
    }
  };

  return (
    <View style={styles.opportunityCard}>
      <View style={styles.opportunityHeader}>
        <View style={styles.opportunityInfo}>
          <Text style={styles.opportunityTitle}>{title}</Text>
          <View style={styles.opportunityMeta}>
            <View style={[styles.impactBadge, { backgroundColor: "rgba(255, 255, 255, 0.1)" }]}>
              <Text style={[styles.impactText, { color: getImpactColor(impact) }]}>{impact}</Text>
            </View>
            <Text style={styles.timeframeText}>{timeframe}</Text>
          </View>
        </View>
      </View>
      <Text style={styles.opportunityDescription}>{description}</Text>
    </View>
  );
}

interface SolutionCardProps {
  title: string;
  description: string;
  benefits: string[];
  investment: string;
}

function SolutionCard({ title, description, benefits, investment }: SolutionCardProps) {
  return (
    <View style={styles.solutionCard}>
      <View style={styles.solutionHeader}>
        <Text style={styles.solutionTitle}>{title}</Text>
        <View style={styles.investmentBadge}>
          <Text style={styles.investmentText}>{investment}</Text>
        </View>
      </View>
      <Text style={styles.solutionDescription}>{description}</Text>
      <View style={styles.benefitsList}>
        <Text style={styles.benefitsTitle}>Key Benefits</Text>
        {benefits.map((benefit, index) => (
          <View key={index} style={styles.benefitItem}>
            <View style={styles.benefitDot} />
            <Text style={styles.benefitText}>{benefit}</Text>
          </View>
        ))}
      </View>
    </View>
  );
}

interface FounderCardProps {
  founder: any;
}

function FounderCard({ founder }: FounderCardProps) {
  return (
    <View style={styles.founderCard}>
      <View style={styles.founderHeader}>
        <View style={styles.founderImageContainer}>
          {founder.photo_url ? (
            <Image source={{ uri: founder.photo_url }} style={styles.founderImage} />
          ) : (
            <View style={styles.founderImagePlaceholder}>
              <Text style={styles.founderInitial}>{founder.name.charAt(0)}</Text>
            </View>
          )}
        </View>
        <View style={styles.founderInfo}>
          <Text style={styles.founderName}>{founder.name}</Text>
          <Text style={styles.founderTitle}>{founder.title}</Text>
          {founder.certifications && (
            <View style={styles.certificationBadge}>
              <Ionicons name="shield-checkmark" size={12} color="#ffffff" />
              <Text style={styles.certificationText}>Certified</Text>
            </View>
          )}
        </View>
      </View>
      
      {founder.quote && (
        <View style={styles.quoteContainer}>
          <Text style={styles.quote}>"{founder.quote}"</Text>
        </View>
      )}
      
      {founder.philosophy && (
        <View style={styles.philosophyContainer}>
          <Text style={styles.philosophyTitle}>Philosophy</Text>
          {founder.philosophy.map((item: string, index: number) => (
            <View key={index} style={styles.philosophyItem}>
              <View style={styles.philosophyDot} />
              <Text style={styles.philosophyText}>{item}</Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
}

export default function IntelligenceProfileScreen() {
  const { token } = useLocalSearchParams<{ token: string }>();
  const [activeSection, setActiveSection] = useState("overview");
  
  const profile = useQuery(api.profiles.getProfileByToken, 
    token ? { token } : "skip"
  );

  const handleSectionPress = (section: string) => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setActiveSection(section);
  };

  const handleContactPress = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    // Handle contact action
  };

  if (!token) {
    router.replace("/");
    return null;
  }

  if (profile === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#000000" />
        <View style={styles.loadingContainer}>
          <View style={styles.loadingSpinner}>
            <ActivityIndicator size="large" color="#ffffff" />
          </View>
          <Text style={styles.loadingText}>Generating your intelligence profile...</Text>
          <Text style={styles.loadingSubtext}>Analyzing business data and market insights</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (profile === null) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#000000" />
        <View style={styles.errorContainer}>
          <View style={styles.errorIcon}>
            <Ionicons name="analytics-outline" size={48} color="#ffffff" />
          </View>
          <Text style={styles.errorTitle}>Profile Being Generated</Text>
          <Text style={styles.errorText}>
            Your AI intelligence profile is currently being prepared by our research team. 
            You'll receive an email notification once it's ready.
          </Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.replace("/")}
          >
            <Text style={styles.backButtonText}>Return to Login</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Handle both enhanced and regular profiles
  const isEnhanced = profile.isEnhanced;
  const companyName = isEnhanced ? profile.company?.name : profile.companyName;
  const industry = isEnhanced ? "Specialized Cleaning Services" : profile.industry;
  const companySize = isEnhanced ? profile.company?.structure : profile.companySize;
  const priorityScore = isEnhanced ? profile.ai_recommendations?.priority_score : profile.priorityScore;

  // Get metrics from enhanced or regular profile
  const keyMetrics = isEnhanced 
    ? Object.entries(profile.key_metrics || {}).map(([label, value]) => ({ label, value }))
    : profile.keyMetrics || [];

  // Get opportunities from enhanced or regular profile
  const quickWins = isEnhanced 
    ? profile.ai_recommendations?.quick_wins || []
    : profile.quickWins || [];

  // Get solutions from enhanced or regular profile
  const strategicSolutions = isEnhanced 
    ? profile.ai_recommendations?.strategic_solutions || []
    : profile.strategicSolutions || [];

  // Get next steps from enhanced or regular profile
  const nextSteps = isEnhanced 
    ? profile.ai_recommendations?.next_steps || []
    : profile.nextSteps || [];

  const renderContent = () => {
    switch (activeSection) {
      case "overview":
        return (
          <View style={styles.sectionContent}>
            {/* Business Overview */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionIcon}>
                  <Ionicons name="business-outline" size={20} color="#ffffff" />
                </View>
                <Text style={styles.sectionTitle}>BUSINESS OVERVIEW</Text>
              </View>

              <View style={styles.card}>
                <Text style={styles.description}>
                  {isEnhanced 
                    ? profile.mission?.statement || "Transforming environments with compassionate, specialized cleaning services."
                    : profile.businessDescription
                  }
                </Text>
              </View>
            </View>

            {/* Key Metrics */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionIcon}>
                  <Ionicons name="analytics-outline" size={20} color="#ffffff" />
                </View>
                <Text style={styles.sectionTitle}>KEY METRICS</Text>
              </View>
              <View style={styles.metricsGrid}>
                {keyMetrics.slice(0, 4).map((metric, index) => (
                  <MetricCard
                    key={index}
                    label={metric.label}
                    value={metric.value}
                    trend={metric.trend}
                  />
                ))}
              </View>
            </View>

            {/* Leadership - Enhanced profiles only */}
            {isEnhanced && profile.leadership?.founder && (
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <View style={styles.sectionIcon}>
                    <Ionicons name="person-outline" size={20} color="#ffffff" />
                  </View>
                  <Text style={styles.sectionTitle}>LEADERSHIP</Text>
                </View>
                <FounderCard founder={profile.leadership.founder} />
              </View>
            )}
          </View>
        );

      case "opportunities":
        return (
          <View style={styles.sectionContent}>
            {/* Quick Wins */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionIcon}>
                  <Ionicons name="flash-outline" size={20} color="#ffffff" />
                </View>
                <Text style={styles.sectionTitle}>IMMEDIATE OPPORTUNITIES</Text>
              </View>
              {quickWins.map((win, index) => (
                <OpportunityCard
                  key={index}
                  title={win.title}
                  description={win.description}
                  impact={win.impact}
                  timeframe={win.timeframe}
                />
              ))}
            </View>

            {/* Strategic Solutions */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionIcon}>
                  <Ionicons name="construct-outline" size={20} color="#ffffff" />
                </View>
                <Text style={styles.sectionTitle}>STRATEGIC SOLUTIONS</Text>
              </View>
              {strategicSolutions.map((solution, index) => (
                <SolutionCard
                  key={index}
                  title={solution.title}
                  description={solution.description}
                  benefits={solution.benefits}
                  investment={solution.investment}
                />
              ))}
            </View>
          </View>
        );

      case "next-steps":
        return (
          <View style={styles.sectionContent}>
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionIcon}>
                  <Ionicons name="arrow-forward-outline" size={20} color="#ffffff" />
                </View>
                <Text style={styles.sectionTitle}>RECOMMENDED NEXT STEPS</Text>
              </View>
              <View style={styles.card}>
                {nextSteps.map((step, index) => (
                  <View key={index} style={styles.stepItem}>
                    <View style={styles.stepNumber}>
                      <Text style={styles.stepNumberText}>{index + 1}</Text>
                    </View>
                    <Text style={styles.stepText}>{step}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* Contact Section */}
            <View style={styles.section}>
              <View style={styles.contactCard}>
                <View style={styles.contactHeader}>
                  <View style={styles.aliasLogo}>
                    <Text style={styles.aliasLogoText}>ALIAS</Text>
                  </View>
                  <Text style={styles.contactTitle}>Ready to Transform Your Business?</Text>
                  <Text style={styles.contactSubtitle}>
                    Let's discuss how ALIAS can implement these AI solutions for your business
                  </Text>
                </View>
                
                <TouchableOpacity 
                  style={styles.contactButton}
                  onPress={handleContactPress}
                >
                  <Ionicons name="calendar-outline" size={20} color="#000000" />
                  <Text style={styles.contactButtonText}>Schedule Consultation</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <View style={styles.logoContainer}>
            <Text style={styles.logo}>ALIAS</Text>
          </View>
          <TouchableOpacity 
            style={styles.logoutButton}
            onPress={() => router.replace("/")}
          >
            <Ionicons name="log-out-outline" size={20} color="#ffffff" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerSubtitle}>AI INTELLIGENCE PROFILE</Text>
          <Text style={styles.companyName}>{companyName}</Text>
          
          <View style={styles.headerMeta}>
            <View style={styles.metaItem}>
              <Text style={styles.metaLabel}>INDUSTRY</Text>
              <Text style={styles.metaValue}>{industry}</Text>
            </View>
            <View style={styles.metaItem}>
              <Text style={styles.metaLabel}>SIZE</Text>
              <Text style={styles.metaValue}>{companySize}</Text>
            </View>
            <View style={styles.metaItem}>
              <Text style={styles.metaLabel}>PRIORITY</Text>
              <View style={styles.priorityContainer}>
                <Text style={styles.priorityScore}>{priorityScore || "8.5"}</Text>
                <Text style={styles.priorityMax}>/10</Text>
              </View>
            </View>
          </View>
        </View>
      </View>

      {/* Navigation Tabs */}
      <View style={styles.navigationTabs}>
        <TouchableOpacity 
          style={[styles.navTab, activeSection === "overview" && styles.navTabActive]}
          onPress={() => handleSectionPress("overview")}
        >
          <Text style={[styles.navTabText, activeSection === "overview" && styles.navTabTextActive]}>
            Overview
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.navTab, activeSection === "opportunities" && styles.navTabActive]}
          onPress={() => handleSectionPress("opportunities")}
        >
          <Text style={[styles.navTabText, activeSection === "opportunities" && styles.navTabTextActive]}>
            Opportunities
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.navTab, activeSection === "next-steps" && styles.navTabActive]}
          onPress={() => handleSectionPress("next-steps")}
        >
          <Text style={[styles.navTabText, activeSection === "next-steps" && styles.navTabTextActive]}>
            Next Steps
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderContent()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    gap: 24,
    paddingHorizontal: 32,
  },
  loadingSpinner: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
    textAlign: "center",
  },
  loadingSubtext: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.7)",
    textAlign: "center",
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 32,
    gap: 20,
  },
  errorIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: "300",
    color: "#ffffff",
    textAlign: "center",
    letterSpacing: 1,
  },
  errorText: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.7)",
    textAlign: "center",
    lineHeight: 24,
  },
  backButton: {
    marginTop: 24,
    paddingHorizontal: 32,
    paddingVertical: 16,
    backgroundColor: "#ffffff",
    borderRadius: 8,
  },
  backButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#000000",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  header: {
    padding: 24,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)",
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 32,
  },
  logoContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
  },
  logo: {
    fontSize: 14,
    fontWeight: "600",
    color: "#ffffff",
    letterSpacing: 2,
  },
  logoutButton: {
    padding: 8,
  },
  headerContent: {
    alignItems: "center",
  },
  headerSubtitle: {
    fontSize: 12,
    fontWeight: "500",
    color: "#ffffff",
    letterSpacing: 3,
    textTransform: "uppercase",
    marginBottom: 8,
  },
  companyName: {
    fontSize: 32,
    fontWeight: "300",
    color: "#ffffff",
    textAlign: "center",
    marginBottom: 24,
    letterSpacing: 1,
  },
  headerMeta: {
    flexDirection: "row",
    gap: 32,
    width: "100%",
    justifyContent: "center",
  },
  metaItem: {
    alignItems: "center",
  },
  metaLabel: {
    fontSize: 10,
    fontWeight: "500",
    color: "#ffffff",
    letterSpacing: 1,
    textTransform: "uppercase",
    marginBottom: 6,
  },
  metaValue: {
    fontSize: 16,
    color: "#ffffff",
    fontWeight: "500",
  },
  priorityContainer: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  priorityScore: {
    fontSize: 20,
    color: "#ffffff",
    fontWeight: "600",
  },
  priorityMax: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.5)",
    fontWeight: "400",
  },
  navigationTabs: {
    flexDirection: "row",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)",
  },
  navTab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    borderRadius: 8,
  },
  navTabActive: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  navTabText: {
    fontSize: 14,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.6)",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  navTabTextActive: {
    color: "#ffffff",
  },
  sectionContent: {
    paddingBottom: 32,
  },
  section: {
    padding: 24,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  sectionIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "500",
    color: "#ffffff",
    letterSpacing: 2,
    textTransform: "uppercase",
  },
  card: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 20,
  },
  description: {
    fontSize: 16,
    color: "#ffffff",
    lineHeight: 24,
  },
  metricsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  metricCard: {
    flex: 1,
    minWidth: "45%",
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 20,
    alignItems: "center",
  },
  metricValue: {
    fontSize: 28,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 6,
  },
  metricLabel: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.7)",
    textAlign: "center",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  metricTrend: {
    fontSize: 11,
    color: "#ffffff",
    marginTop: 4,
    fontWeight: "500",
  },
  founderCard: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 20,
  },
  founderHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  founderImageContainer: {
    marginRight: 16,
  },
  founderImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  founderImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
  },
  founderInitial: {
    fontSize: 24,
    fontWeight: "600",
    color: "#ffffff",
  },
  founderInfo: {
    flex: 1,
  },
  founderName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 4,
  },
  founderTitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.7)",
    marginBottom: 8,
  },
  certificationBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
    gap: 4,
  },
  certificationText: {
    fontSize: 10,
    color: "#ffffff",
    fontWeight: "500",
    textTransform: "uppercase",
  },
  quoteContainer: {
    marginBottom: 16,
    paddingLeft: 16,
    borderLeftWidth: 3,
    borderLeftColor: "rgba(255, 255, 255, 0.3)",
  },
  quote: {
    fontSize: 16,
    color: "#ffffff",
    fontStyle: "italic",
    lineHeight: 24,
  },
  philosophyContainer: {
    gap: 8,
  },
  philosophyTitle: {
    fontSize: 12,
    fontWeight: "500",
    color: "#ffffff",
    textTransform: "uppercase",
    letterSpacing: 0.5,
    marginBottom: 8,
  },
  philosophyItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  philosophyDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: "#ffffff",
  },
  philosophyText: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.9)",
    flex: 1,
  },
  opportunityCard: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
  },
  opportunityHeader: {
    marginBottom: 12,
  },
  opportunityInfo: {
    flex: 1,
  },
  opportunityTitle: {
    fontSize: 18,
    fontWeight: "500",
    color: "#ffffff",
    marginBottom: 8,
  },
  opportunityMeta: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  impactBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  impactText: {
    fontSize: 10,
    fontWeight: "600",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  timeframeText: {
    fontSize: 10,
    color: "rgba(255, 255, 255, 0.6)",
  },
  opportunityDescription: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.9)",
    lineHeight: 20,
  },
  solutionCard: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
  },
  solutionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  solutionTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "500",
    color: "#ffffff",
    marginRight: 16,
  },
  investmentBadge: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  investmentText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#ffffff",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  solutionDescription: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.9)",
    lineHeight: 20,
    marginBottom: 16,
  },
  benefitsList: {
    gap: 8,
  },
  benefitsTitle: {
    fontSize: 12,
    fontWeight: "500",
    color: "#ffffff",
    textTransform: "uppercase",
    letterSpacing: 0.5,
    marginBottom: 8,
  },
  benefitItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  benefitDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: "#ffffff",
  },
  benefitText: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.9)",
    flex: 1,
  },
  stepItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  stepNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: "#ffffff",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  stepNumberText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#000000",
  },
  stepText: {
    flex: 1,
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.9)",
    lineHeight: 20,
    marginTop: 2,
  },
  contactCard: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 24,
    alignItems: "center",
  },
  contactHeader: {
    alignItems: "center",
    marginBottom: 24,
  },
  aliasLogo: {
    width: 60,
    height: 60,
    borderRadius: 12,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  aliasLogoText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#ffffff",
    letterSpacing: 2,
  },
  contactTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#ffffff",
    textAlign: "center",
    marginBottom: 8,
  },
  contactSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.7)",
    textAlign: "center",
    lineHeight: 20,
  },
  contactButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    backgroundColor: "#ffffff",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 8,
  },
  contactButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000000",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
});