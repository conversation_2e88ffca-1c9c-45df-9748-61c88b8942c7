import React from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Dimensions,
  Image,
} from "react-native";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useLocalSearchParams, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

const { width } = Dimensions.get("window");

interface ActionButtonProps {
  icon: string;
  label: string;
  onPress: () => void;
}

function ActionButton({ icon, label, onPress }: ActionButtonProps) {
  return (
    <TouchableOpacity style={styles.actionButton} onPress={onPress}>
      <View style={styles.actionIcon}>
        <Ionicons name={icon as any} size={20} color="#ffffff" />
      </View>
      <Text style={styles.actionLabel}>{label.toUpperCase()}</Text>
    </TouchableOpacity>
  );
}

interface InfoSectionProps {
  title: string;
  children: React.ReactNode;
}

function InfoSection({ title, children }: InfoSectionProps) {
  return (
    <View style={styles.infoSection}>
      <Text style={styles.infoSectionTitle}>{title.toUpperCase()}</Text>
      {children}
    </View>
  );
}

export default function UserProfileScreen() {
  const { token } = useLocalSearchParams<{ token: string }>();
  const user = useQuery(api.auth.getUserByToken, token ? { token } : "skip");

  if (!token) {
    router.replace("/");
    return null;
  }

  if (user === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (user === null) {
    router.replace("/");
    return null;
  }

  const handleBack = () => {
    router.back();
  };

  const handleStarred = () => {
    console.log("Toggle starred");
  };

  const handleContact = () => {
    console.log("Contact user");
  };

  const handleNote = () => {
    console.log("Add note");
  };

  const handleCadence = () => {
    console.log("Set cadence");
  };

  const handleGroup = () => {
    console.log("Add to group");
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case "super_admin": return "Super Administrator";
      case "admin": return "Administrator";
      case "dev": return "Developer";
      case "consultant": return "Consultant";
      case "stakeholder": return "Stakeholder";
      case "member": return "Team Member";
      case "customer": return "Customer";
      case "guest": return "Guest";
      default: return "User";
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "super_admin":
      case "admin":
        return "#ffffff";
      case "dev":
        return "#4ade80";
      case "consultant":
        return "#3b82f6";
      case "stakeholder":
        return "#f59e0b";
      case "member":
        return "#8b5cf6";
      case "customer":
        return "#06b6d4";
      default:
        return "rgba(255, 255, 255, 0.6)";
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="chevron-back" size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        {/* Profile Image */}
        <View style={styles.profileImageContainer}>
          <View style={styles.profileImageWrapper}>
            {user.avatar ? (
              <Image source={{ uri: user.avatar }} style={styles.profileImage} />
            ) : (
              <View style={styles.profileImagePlaceholder}>
                <Text style={styles.profileImageInitial}>
                  {user.name.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* User Info */}
        <View style={styles.userInfo}>
          <View style={styles.nameContainer}>
            <Text style={styles.userName}>{user.name}</Text>
            {user.isVerified && (
              <Ionicons name="checkmark-circle" size={20} color="#4ade80" />
            )}
          </View>
          
          <Text style={styles.userLocation}>
            {user.location || "Location not specified"}
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <ActionButton icon="star-outline" label="Starred" onPress={handleStarred} />
          <ActionButton icon="chatbubble-outline" label="Contact" onPress={handleContact} />
          <ActionButton icon="document-text-outline" label="Note" onPress={handleNote} />
          <ActionButton icon="radio-outline" label="Cadence" onPress={handleCadence} />
          <ActionButton icon="people-outline" label="Group" onPress={handleGroup} />
        </View>

        {/* Groups */}
        <View style={styles.groupsSection}>
          <Text style={styles.groupsTitle}>GROUPS</Text>
          <View style={styles.groupBadge}>
            <Ionicons name="star" size={16} color="#ffffff" />
            <Text style={styles.groupBadgeText}>Starred</Text>
          </View>
        </View>

        {/* Bio/Info Sections */}
        <InfoSection title="Bio">
          <Text style={styles.bioText}>
            {user.title || getRoleDisplayName(user.role)} at {user.company || "ALIAS AI"}
          </Text>
          {user.email && (
            <TouchableOpacity style={styles.emailLink}>
              <Text style={styles.emailText}>{user.email}</Text>
            </TouchableOpacity>
          )}
        </InfoSection>

        <InfoSection title="Role">
          <View style={styles.roleContainer}>
            <View style={[styles.roleBadge, { backgroundColor: getRoleBadgeColor(user.role) + "20" }]}>
              <Text style={[styles.roleText, { color: getRoleBadgeColor(user.role) }]}>
                {getRoleDisplayName(user.role)}
              </Text>
            </View>
          </View>
        </InfoSection>

        {user.company && (
          <InfoSection title="Company">
            <Text style={styles.companyText}>{user.company}</Text>
          </InfoSection>
        )}

        <InfoSection title="Social">
          <View style={styles.socialIcons}>
            <TouchableOpacity style={styles.socialIcon}>
              <Ionicons name="logo-linkedin" size={24} color="rgba(255, 255, 255, 0.6)" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.socialIcon}>
              <Ionicons name="logo-twitter" size={24} color="rgba(255, 255, 255, 0.6)" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.socialIcon}>
              <Ionicons name="logo-youtube" size={24} color="rgba(255, 255, 255, 0.6)" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.socialIcon}>
              <Ionicons name="logo-github" size={24} color="rgba(255, 255, 255, 0.6)" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.socialIcon}>
              <Ionicons name="logo-facebook" size={24} color="rgba(255, 255, 255, 0.6)" />
            </TouchableOpacity>
          </View>
        </InfoSection>

        <InfoSection title="Highlights">
          <View style={styles.highlightsContainer}>
            <View style={styles.highlightItem}>
              <Text style={styles.highlightLabel}>Member since</Text>
              <Text style={styles.highlightValue}>
                {new Date(user.createdAt).getFullYear()}
              </Text>
            </View>
            <View style={styles.highlightItem}>
              <Text style={styles.highlightLabel}>Projects</Text>
              <Text style={styles.highlightValue}>12</Text>
            </View>
            <View style={styles.highlightItem}>
              <Text style={styles.highlightLabel}>Collaborations</Text>
              <Text style={styles.highlightValue}>8</Text>
            </View>
          </View>
        </InfoSection>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.7)",
    textAlign: "center",
  },
  header: {
    padding: 24,
    paddingBottom: 0,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  profileImageContainer: {
    alignItems: "center",
    marginVertical: 32,
  },
  profileImageWrapper: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 2,
    borderColor: "rgba(255, 255, 255, 0.2)",
    overflow: "hidden",
  },
  profileImage: {
    width: "100%",
    height: "100%",
  },
  profileImagePlaceholder: {
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  profileImageInitial: {
    fontSize: 48,
    fontWeight: "600",
    color: "#ffffff",
  },
  userInfo: {
    alignItems: "center",
    marginBottom: 32,
    paddingHorizontal: 24,
  },
  nameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 8,
  },
  userName: {
    fontSize: 24,
    fontWeight: "600",
    color: "#ffffff",
    textAlign: "center",
  },
  userLocation: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
    textAlign: "center",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  actionButtons: {
    flexDirection: "row",
    paddingHorizontal: 24,
    marginBottom: 32,
    gap: 8,
  },
  actionButton: {
    flex: 1,
    alignItems: "center",
    gap: 8,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  actionLabel: {
    fontSize: 10,
    color: "rgba(255, 255, 255, 0.8)",
    fontWeight: "500",
    letterSpacing: 0.5,
  },
  groupsSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  groupsTitle: {
    fontSize: 12,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.6)",
    letterSpacing: 1,
    marginBottom: 12,
  },
  groupBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignSelf: "flex-start",
    gap: 6,
  },
  groupBadgeText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#ffffff",
  },
  infoSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  infoSectionTitle: {
    fontSize: 12,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.6)",
    letterSpacing: 1,
    marginBottom: 12,
  },
  bioText: {
    fontSize: 16,
    color: "#ffffff",
    lineHeight: 24,
    marginBottom: 8,
  },
  emailLink: {
    alignSelf: "flex-start",
  },
  emailText: {
    fontSize: 16,
    color: "#ffffff",
    textDecorationLine: "underline",
  },
  roleContainer: {
    flexDirection: "row",
  },
  roleBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: "flex-start",
  },
  roleText: {
    fontSize: 14,
    fontWeight: "500",
  },
  companyText: {
    fontSize: 16,
    color: "#ffffff",
  },
  socialIcons: {
    flexDirection: "row",
    gap: 16,
  },
  socialIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    alignItems: "center",
    justifyContent: "center",
  },
  highlightsContainer: {
    flexDirection: "row",
    gap: 24,
  },
  highlightItem: {
    alignItems: "center",
  },
  highlightLabel: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.6)",
    marginBottom: 4,
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  highlightValue: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
  },
});