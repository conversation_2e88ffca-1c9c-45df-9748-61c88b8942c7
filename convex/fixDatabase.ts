import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Clean up database and fix schema issues
export const cleanupDatabase = mutation({
  args: {},
  handler: async (ctx, args) => {
    // Delete the problematic user
    const problemUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", "<EMAIL>"))
      .unique();
    
    if (problemUser) {
      // Delete related sessions first
      const sessions = await ctx.db
        .query("sessions")
        .withIndex("by_user", (q) => q.eq("userId", problemUser._id))
        .collect();
      
      for (const session of sessions) {
        await ctx.db.delete(session._id);
      }
      
      // Delete related profiles
      const profiles = await ctx.db
        .query("clientProfiles")
        .withIndex("by_client", (q) => q.eq("clientId", problemUser._id))
        .collect();
      
      for (const profile of profiles) {
        await ctx.db.delete(profile._id);
      }
      
      const enhancedProfiles = await ctx.db
        .query("enhancedClientProfiles")
        .withIndex("by_client", (q) => q.eq("clientId", problemUser._id))
        .collect();
      
      for (const profile of enhancedProfiles) {
        await ctx.db.delete(profile._id);
      }
      
      // Delete the user
      await ctx.db.delete(problemUser._id);
    }
    
    // Fix all users without roles
    const users = await ctx.db.query("users").collect();
    let fixedCount = 0;
    
    for (const user of users) {
      if (!user.role) {
        await ctx.db.patch(user._id, {
          role: "guest",
        });
        fixedCount++;
      }
    }
    
    return { 
      success: true,
      message: `Cleaned up database. Fixed ${fixedCount} users without roles.`,
      deletedProblemUser: !!problemUser
    };
  },
});

// Fix users without roles
export const fixUsersWithoutRoles = mutation({
  args: {},
  handler: async (ctx, args) => {
    const users = await ctx.db.query("users").collect();
    let fixedCount = 0;
    
    for (const user of users) {
      if (!user.role) {
        await ctx.db.patch(user._id, {
          role: "guest",
        });
        fixedCount++;
      }
    }
    
    return { 
      success: true,
      message: `Fixed ${fixedCount} users without roles`,
      fixedCount 
    };
  },
});