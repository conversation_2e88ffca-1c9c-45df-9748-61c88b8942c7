import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Migration to fix users without roles
export const fixUsersWithoutRoles = mutation({
  args: {},
  handler: async (ctx, args) => {
    const users = await ctx.db.query("users").collect();
    let fixedCount = 0;
    
    for (const user of users) {
      if (!user.role) {
        await ctx.db.patch(user._id, {
          role: "guest",
        });
        fixedCount++;
        console.log(`Fixed user ${user.email} - assigned role: guest`);
      }
    }
    
    return { 
      success: true,
      message: `Fixed ${fixedCount} users without roles`,
      fixedCount 
    };
  },
});

// Alternative: Delete the problematic user if it's just test data
export const deleteUserByEmail = mutation({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();
    
    if (!user) {
      return { success: false, message: "User not found" };
    }
    
    // Delete all related data first
    const sessions = await ctx.db
      .query("sessions")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();
    
    for (const session of sessions) {
      await ctx.db.delete(session._id);
    }
    
    const profiles = await ctx.db
      .query("clientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", user._id))
      .collect();
    
    for (const profile of profiles) {
      await ctx.db.delete(profile._id);
    }
    
    const enhancedProfiles = await ctx.db
      .query("enhancedClientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", user._id))
      .collect();
    
    for (const profile of enhancedProfiles) {
      await ctx.db.delete(profile._id);
    }
    
    await ctx.db.delete(user._id);
    return { 
      success: true, 
      message: `Deleted user: ${args.email}` 
    };
  },
});

// Fix specific user by ID
export const fixUserById = mutation({
  args: {
    userId: v.id("users"),
    role: v.optional(v.union(
      v.literal("admin"),
      v.literal("super_admin"),
      v.literal("dev"),
      v.literal("consultant"),
      v.literal("stakeholder"),
      v.literal("member"),
      v.literal("customer"),
      v.literal("guest")
    )),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    
    if (!user) {
      return { success: false, message: "User not found" };
    }
    
    await ctx.db.patch(args.userId, {
      role: args.role || "guest",
    });
    
    return { 
      success: true, 
      message: `Fixed user ${user.email} - assigned role: ${args.role || "guest"}` 
    };
  },
});