import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Create admin user
export const createAdminUser = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if admin already exists
    const existingAdmin = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", "<EMAIL>"))
      .first();

    if (existingAdmin) {
      return { message: "Admin user already exists", userId: existingAdmin._id };
    }

    // Create admin user
    const adminId = await ctx.db.insert("users", {
      email: "<EMAIL>",
      name: "ALIAS Administrator",
      company: "ALIAS AI",
      role: "admin",
      title: "System Administrator",
      location: "Global",
      isVerified: true,
      createdAt: Date.now(),
    });

    return { message: "Admin user created successfully", userId: adminId };
  },
});

// Create dev bypass user
export const createDevBypass = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if dev user already exists
    const existingDev = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", "<EMAIL>"))
      .first();

    if (existingDev) {
      return { message: "Dev user already exists", userId: existingDev._id };
    }

    // Create dev user
    const devId = await ctx.db.insert("users", {
      email: "<EMAIL>",
      name: "Developer",
      company: "ALIAS AI",
      role: "dev",
      title: "Software Developer",
      location: "Development",
      isVerified: true,
      createdAt: Date.now(),
    });

    return { message: "Dev user created successfully", userId: devId };
  },
});

// Create sample profile for TechStartup Inc.
export const createSampleProfile = mutation({
  args: {},
  handler: async (ctx) => {
    // First create or get the user
    let user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", "<EMAIL>"))
      .first();

    if (!user) {
      const userId = await ctx.db.insert("users", {
        email: "<EMAIL>",
        name: "Sarah Chen",
        company: "TechStartup Inc.",
        role: "stakeholder",
        title: "CEO & Founder",
        location: "San Francisco, CA",
        isVerified: true,
        createdAt: Date.now(),
      });
      user = await ctx.db.get(userId);
    }

    if (!user) throw new Error("Failed to create user");

    // Check if profile already exists
    const existingProfile = await ctx.db
      .query("clientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", user._id))
      .first();

    if (existingProfile) {
      return { message: "Sample profile already exists", profileId: existingProfile._id };
    }

    // Create the client profile
    const profileId = await ctx.db.insert("clientProfiles", {
      clientId: user._id,
      companyName: "TechStartup Inc.",
      industry: "Technology & Software",
      companySize: "25-50 employees",
      website: "https://techstartup.com",
      businessDescription: "A fast-growing SaaS company specializing in project management tools for remote teams. We've experienced 300% growth in the past year and are looking to scale our operations efficiently while maintaining our innovative edge.",
      keyMetrics: [
        { label: "Monthly Recurring Revenue", value: "$125K", trend: "+45% YoY" },
        { label: "Customer Acquisition Cost", value: "$89", trend: "-12% YoY" },
        { label: "Customer Lifetime Value", value: "$2,400", trend: "+28% YoY" },
        { label: "Team Productivity Score", value: "8.2/10", trend: "+15% YoY" }
      ],
      industryChallenges: [
        "Scaling customer support without proportional cost increases",
        "Managing technical debt while maintaining rapid development cycles",
        "Competing with established enterprise solutions",
        "Retaining top talent in a competitive market"
      ],
      marketTrends: [
        "Increased demand for remote work solutions",
        "Growing emphasis on AI-powered productivity tools",
        "Shift towards integrated workflow platforms",
        "Rising importance of data security and compliance"
      ],
      competitiveAdvantages: [
        "Intuitive user interface with minimal learning curve",
        "Strong focus on remote team collaboration",
        "Rapid feature development and deployment",
        "Excellent customer satisfaction scores (4.8/5)"
      ],
      quickWins: [
        {
          title: "AI-Powered Customer Support Chatbot",
          description: "Implement an intelligent chatbot to handle 70% of common support queries, reducing response time from hours to seconds.",
          impact: "High",
          timeframe: "2-4 weeks"
        },
        {
          title: "Automated User Onboarding",
          description: "Create personalized onboarding flows that adapt based on user behavior and company size.",
          impact: "High",
          timeframe: "3-6 weeks"
        },
        {
          title: "Predictive Analytics Dashboard",
          description: "Build AI-driven insights to help customers optimize their project timelines and resource allocation.",
          impact: "Medium",
          timeframe: "4-8 weeks"
        }
      ],
      strategicSolutions: [
        {
          title: "Enterprise AI Integration Platform",
          description: "Develop a comprehensive AI layer that integrates with existing tools and provides intelligent automation across all business processes.",
          benefits: [
            "Reduce manual work by 60-80%",
            "Improve decision-making with predictive analytics",
            "Scale operations without proportional headcount increases",
            "Enhance competitive positioning in enterprise market"
          ],
          investment: "Medium"
        },
        {
          title: "Intelligent Resource Optimization System",
          description: "Create an AI system that automatically optimizes team assignments, project timelines, and resource allocation based on historical data and real-time performance metrics.",
          benefits: [
            "Increase team productivity by 35%",
            "Reduce project delays by 50%",
            "Optimize resource utilization",
            "Provide data-driven insights for strategic planning"
          ],
          investment: "High"
        }
      ],
      nextSteps: [
        "Schedule a technical discovery session to assess current infrastructure",
        "Conduct stakeholder interviews to prioritize AI implementation areas",
        "Develop a phased implementation roadmap with clear milestones",
        "Begin with the AI-powered customer support chatbot as a proof of concept",
        "Establish success metrics and monitoring systems for AI initiatives"
      ],
      priorityScore: 8.7,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return { message: "Sample profile created successfully", profileId };
  },
});

// Create Mrs. Muscle profile
export const createMrsMuscleProfile = mutation({
  args: {},
  handler: async (ctx) => {
    // First create or get the user
    let user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", "<EMAIL>"))
      .first();

    if (!user) {
      const userId = await ctx.db.insert("users", {
        email: "<EMAIL>",
        name: "Arlene Thompson",
        company: "Mrs. Muscle Cleaning Services",
        role: "stakeholder",
        title: "Founder & CEO",
        location: "Melbourne, Australia",
        isVerified: true,
        createdAt: Date.now(),
      });
      user = await ctx.db.get(userId);
    }

    if (!user) throw new Error("Failed to create user");

    // Check if enhanced profile already exists
    const existingProfile = await ctx.db
      .query("enhancedClientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", user._id))
      .first();

    if (existingProfile) {
      return { message: "Mrs. Muscle profile already exists", profileId: existingProfile._id };
    }

    // Create the enhanced client profile
    const profileId = await ctx.db.insert("enhancedClientProfiles", {
      clientId: user._id,
      company: {
        name: "Mrs. Muscle Cleaning Services",
        tagline: "Transforming spaces with compassion and expertise",
        structure: "Family-owned business",
        established: "2018",
        location: "Melbourne, Australia",
        certification: "Certified trauma and biohazard cleaning specialist"
      },
      leadership: {
        founder: {
          name: "Arlene Thompson",
          title: "Founder & CEO",
          certifications: ["Trauma Scene Cleaning", "Biohazard Remediation", "IICRC Certified"],
          photo_url: "",
          journey: {
            "2018": "Founded Mrs. Muscle after personal experience with inadequate cleaning services",
            "2019": "Obtained specialized trauma cleaning certifications",
            "2020": "Expanded to serve healthcare facilities during pandemic",
            "2021": "Launched compassionate cleaning program for vulnerable families",
            "2022": "Achieved 500+ successful specialized cleaning projects"
          },
          philosophy: [
            "Every space deserves dignity and respect",
            "Compassion is as important as technical expertise",
            "No job is too challenging when approached with the right mindset",
            "Building trust through transparency and reliability"
          ],
          quote: "We don't just clean spaces; we restore hope and dignity to people's lives during their most challenging moments.",
          mission_quote: "To provide exceptional cleaning services that go beyond surface-level results, creating environments where people can heal, thrive, and feel truly at home."
        }
      },
      mission: {
        statement: "To provide exceptional cleaning services that go beyond surface-level results, creating environments where people can heal, thrive, and feel truly at home.",
        attributed_to: "Arlene Thompson, Founder"
      },
      core_values: [
        "Compassion in every interaction",
        "Excellence without compromise",
        "Respect for every space and story",
        "Transparency in all communications",
        "Continuous learning and improvement"
      ],
      services: {
        specialized: [
          {
            name: "Trauma Scene Cleaning",
            description: "Professional, compassionate cleaning of crime scenes, accidents, and traumatic events",
            category: "Specialized Cleaning"
          },
          {
            name: "Biohazard Remediation",
            description: "Safe removal and disposal of biological contaminants and hazardous materials",
            category: "Specialized Cleaning"
          },
          {
            name: "Hoarding Cleanup",
            description: "Sensitive, non-judgmental cleaning and organization services for hoarding situations",
            category: "Specialized Cleaning"
          },
          {
            name: "Medical Facility Cleaning",
            description: "Hospital-grade cleaning for healthcare facilities and medical practices",
            category: "Healthcare"
          }
        ],
        standard: [
          {
            name: "Deep House Cleaning",
            description: "Comprehensive residential cleaning including all rooms and surfaces",
            guarantee: "100% satisfaction or we return to make it right"
          },
          {
            name: "Office Cleaning",
            description: "Professional commercial cleaning for offices and business spaces",
            guarantee: "Consistent quality with regular quality checks"
          },
          {
            name: "Move-in/Move-out Cleaning",
            description: "Thorough cleaning for property transitions",
            guarantee: "Bond-back guarantee for rental properties"
          }
        ],
        focus_areas: [
          "Trauma-informed cleaning approaches",
          "Eco-friendly cleaning solutions",
          "Flexible scheduling for urgent needs",
          "Comprehensive insurance coverage"
        ]
      },
      service_coverage: {
        primary_markets: ["Melbourne CBD", "Inner Melbourne", "Eastern Suburbs"],
        extended_reach: ["Outer Melbourne", "Regional Victoria (by arrangement)"]
      },
      key_metrics: {
        "Years in Operation": "6 years",
        "Specialized Projects Completed": "500+",
        "Customer Satisfaction Rate": "98.5%",
        "Average Response Time": "2 hours",
        "Team Size": "12 specialists",
        "Certifications Held": "15+",
        "Insurance Coverage": "$5M liability",
        "Repeat Customer Rate": "85%"
      },
      target_markets: {
        high_value_segments: [
          {
            name: "Healthcare Facilities",
            description: "Hospitals, clinics, and medical practices requiring specialized cleaning",
            category: "Healthcare",
            revenue_indicator: "High-value contracts"
          },
          {
            name: "Insurance Companies",
            description: "Trauma and biohazard cleaning for insurance claims",
            category: "Insurance",
            revenue_indicator: "Consistent volume"
          },
          {
            name: "Property Management",
            description: "Specialized cleaning for property managers dealing with difficult situations",
            category: "Real Estate",
            revenue_indicator: "Recurring partnerships"
          }
        ],
        standard_segments: [
          {
            name: "Residential Clients",
            description: "Homeowners requiring deep cleaning and maintenance services"
          },
          {
            name: "Small Businesses",
            description: "Local businesses needing regular commercial cleaning"
          }
        ],
        strategic_partnerships: [
          {
            name: "Victim Support Services",
            description: "Partnerships with organizations supporting trauma victims",
            category: "Community"
          },
          {
            name: "Healthcare Networks",
            description: "Preferred vendor relationships with medical facilities",
            category: "Healthcare"
          }
        ],
        revenue_mix: {
          "Specialized Cleaning": "60%",
          "Standard Residential": "25%",
          "Commercial Cleaning": "15%"
        },
        growth_drivers: [
          "Increasing awareness of specialized cleaning needs",
          "Growing healthcare sector",
          "Word-of-mouth referrals from satisfied clients",
          "Insurance industry partnerships"
        ],
        key_metrics: {
          "Average Project Value": "$850",
          "Customer Lifetime Value": "$3,200",
          "Referral Rate": "45%",
          "Market Share (Specialized)": "12%"
        }
      },
      digital_presence: {
        platforms: [
          {
            name: "Website",
            description: "Professional website with service information and booking system",
            url: "https://mrsmuscle.com.au",
            design_quality: "Professional",
            service_listings: "Comprehensive",
            testimonials: "Featured prominently"
          },
          {
            name: "Google My Business",
            followers: "2,500+",
            views: "15,000/month",
            engagement: "High",
            description: "Strong local presence with excellent reviews"
          },
          {
            name: "Facebook",
            followers: "3,200",
            engagement: "Moderate",
            content_focus: "Educational content about cleaning",
            description: "Community-focused content and customer testimonials"
          }
        ],
        content_strategy: [
          "Educational posts about cleaning and safety",
          "Behind-the-scenes content showing professionalism",
          "Customer testimonials and success stories",
          "Community involvement and charity work"
        ],
        growth_opportunities: [
          "Video content showcasing expertise",
          "LinkedIn presence for B2B opportunities",
          "SEO optimization for specialized services",
          "Online booking system enhancement"
        ],
        review_sentiment: {
          positive: "96%",
          praised_for: [
            "Professionalism and compassion",
            "Thoroughness of cleaning",
            "Reliability and punctuality",
            "Handling of sensitive situations"
          ],
          improvement_areas: [
            "Pricing transparency",
            "Availability during peak times"
          ]
        }
      },
      competitive_landscape: {
        market_position: "Premium specialized cleaning provider with strong reputation for compassionate service",
        competitors: [
          {
            name: "CleanCorp Melbourne",
            position: "Large commercial cleaner",
            focus: "Volume-based commercial cleaning",
            approach: "Standardized processes",
            scale: "Large",
            pricing: "Competitive",
            description: "Major competitor in commercial space but lacks specialized services"
          },
          {
            name: "Trauma Clean Australia",
            position: "Specialized trauma cleaning",
            focus: "Trauma and biohazard only",
            approach: "Technical expertise focused",
            scale: "Medium",
            description: "Direct competitor in specialized cleaning but less personal approach"
          }
        ],
        competitive_advantages: [
          "Unique combination of technical expertise and compassionate approach",
          "Strong local reputation and community connections",
          "Comprehensive insurance and certifications",
          "Flexible service offerings from standard to specialized",
          "Family-owned business with personal touch"
        ],
        market_positioning: {
          specialization_advantage: "Only local provider combining trauma cleaning with standard services",
          emotional_differentiation: "Compassion-first approach sets apart from purely technical competitors"
        },
        strength_ratings: {
          "Technical Expertise": "9/10",
          "Customer Service": "10/10",
          "Market Reputation": "9/10",
          "Pricing Competitiveness": "7/10",
          "Service Range": "8/10"
        },
        strategic_insight: "Mrs. Muscle has successfully differentiated through compassionate service delivery, creating strong customer loyalty and word-of-mouth growth."
      },
      impact_and_recognition: {
        achievements: [
          "Featured in Melbourne Business Journal for community service",
          "Recipient of Small Business Excellence Award 2023",
          "Certified by multiple industry bodies",
          "Zero workplace accidents in 6 years of operation"
        ],
        partnerships: [
          "Victorian Police Victim Support",
          "Melbourne Healthcare Network",
          "Local Real Estate Institute"
        ],
        vision_statement: "To be Australia's most trusted and compassionate cleaning service, setting the standard for how specialized cleaning should be delivered with dignity and care."
      },
      operational_details: {
        culture: "Family-oriented, compassionate, professional excellence",
        approach: "Trauma-informed, client-centered service delivery",
        project_scale: "From single-room cleanups to large commercial facilities",
        specializations: [
          "Trauma scene cleaning",
          "Biohazard remediation",
          "Healthcare facility cleaning",
          "Hoarding situation cleanup"
        ]
      },
      ai_recommendations: {
        quick_wins: [
          {
            title: "AI-Powered Scheduling Optimization",
            description: "Implement intelligent scheduling system that optimizes routes, considers job complexity, and manages emergency callouts efficiently.",
            impact: "High",
            timeframe: "4-6 weeks"
          },
          {
            title: "Automated Customer Communication System",
            description: "Deploy AI chatbot for initial inquiries and automated follow-up system for customer satisfaction and maintenance reminders.",
            impact: "High",
            timeframe: "3-4 weeks"
          },
          {
            title: "Predictive Inventory Management",
            description: "AI system to predict cleaning supply needs based on job types, seasonal patterns, and usage history.",
            impact: "Medium",
            timeframe: "6-8 weeks"
          }
        ],
        strategic_solutions: [
          {
            title: "Comprehensive Business Intelligence Platform",
            description: "Develop AI-driven analytics platform providing insights into customer patterns, operational efficiency, and growth opportunities.",
            benefits: [
              "Increase operational efficiency by 40%",
              "Improve customer retention through predictive service needs",
              "Optimize pricing strategies based on market analysis",
              "Identify new market opportunities through data analysis"
            ],
            investment: "Medium"
          },
          {
            title: "AI-Enhanced Training and Quality Assurance System",
            description: "Create intelligent training modules and quality monitoring system using AI to ensure consistent service delivery and continuous improvement.",
            benefits: [
              "Reduce training time by 50%",
              "Ensure consistent service quality across all jobs",
              "Identify improvement opportunities in real-time",
              "Scale operations while maintaining quality standards"
            ],
            investment: "High"
          }
        ],
        next_steps: [
          "Conduct detailed operational assessment to identify automation opportunities",
          "Implement AI scheduling system as pilot project",
          "Develop customer communication automation",
          "Create comprehensive data collection strategy",
          "Plan phased rollout of AI solutions with staff training"
        ],
        priority_score: 9.2
      },
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return { message: "Mrs. Muscle profile created successfully", profileId };
  },
});

// Fix users without roles
export const fixUsersWithoutRoles = mutation({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();
    let fixedCount = 0;

    for (const user of users) {
      if (!user.role) {
        await ctx.db.patch(user._id, {
          role: "guest", // Default role for users without roles
        });
        fixedCount++;
      }
    }

    return { message: `Fixed ${fixedCount} users without roles` };
  },
});