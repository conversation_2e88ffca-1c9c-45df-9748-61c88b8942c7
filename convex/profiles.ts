import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createOrUpdateProfile = mutation({
  args: {
    clientEmail: v.string(),
    profileData: v.object({
      companyName: v.string(),
      industry: v.string(),
      companySize: v.string(),
      website: v.optional(v.string()),
      businessDescription: v.string(),
      keyMetrics: v.array(v.object({
        label: v.string(),
        value: v.string(),
        trend: v.optional(v.string()),
      })),
      industryChallenges: v.array(v.string()),
      marketTrends: v.array(v.string()),
      competitiveAdvantages: v.array(v.string()),
      quickWins: v.array(v.object({
        title: v.string(),
        description: v.string(),
        impact: v.string(),
        timeframe: v.string(),
      })),
      strategicSolutions: v.array(v.object({
        title: v.string(),
        description: v.string(),
        benefits: v.array(v.string()),
        investment: v.string(),
      })),
      nextSteps: v.array(v.string()),
      priorityScore: v.number(),
    }),
  },
  handler: async (ctx, args) => {
    // Find or create user
    let user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.clientEmail))
      .unique();

    if (!user) {
      const userId = await ctx.db.insert("users", {
        email: args.clientEmail,
        name: args.profileData.companyName,
        company: args.profileData.companyName,
        role: "stakeholder",
        createdAt: Date.now(),
      });
      user = await ctx.db.get(userId);
    } else {
      // If user exists but doesn't have a role, update them
      if (!user.role) {
        await ctx.db.patch(user._id, {
          role: "stakeholder",
        });
        user = await ctx.db.get(user._id);
      }
    }

    if (!user) throw new Error("Failed to create user");

    // Check if profile exists
    const existingProfile = await ctx.db
      .query("clientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", user._id))
      .unique();

    const profileData = {
      clientId: user._id,
      ...args.profileData,
      updatedAt: Date.now(),
    };

    if (existingProfile) {
      await ctx.db.patch(existingProfile._id, profileData);
      return existingProfile._id;
    } else {
      return await ctx.db.insert("clientProfiles", {
        ...profileData,
        createdAt: Date.now(),
      });
    }
  },
});

export const getClientProfile = query({
  args: {
    clientId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const profile = await ctx.db
      .query("clientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", args.clientId))
      .unique();

    if (!profile) return null;

    const user = await ctx.db.get(args.clientId);
    return {
      ...profile,
      client: user,
    };
  },
});

export const getProfileByToken = query({
  args: {
    token: v.string(),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db
      .query("sessions")
      .withIndex("by_token", (q) => q.eq("token", args.token))
      .unique();

    if (!session || session.expiresAt < Date.now()) {
      return null;
    }

    // First try enhanced profile
    const enhancedProfile = await ctx.db
      .query("enhancedClientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", session.userId))
      .unique();

    if (enhancedProfile) {
      const user = await ctx.db.get(session.userId);
      return {
        ...enhancedProfile,
        client: user,
        isEnhanced: true,
      };
    }

    // Fall back to regular profile
    const profile = await ctx.db
      .query("clientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", session.userId))
      .unique();

    if (!profile) return null;

    const user = await ctx.db.get(session.userId);
    return {
      ...profile,
      client: user,
      isEnhanced: false,
    };
  },
});

// Migration function to fix existing users without roles
export const fixUsersWithoutRoles = mutation({
  args: {},
  handler: async (ctx, args) => {
    const users = await ctx.db.query("users").collect();
    let fixedCount = 0;
    
    for (const user of users) {
      if (!user.role) {
        await ctx.db.patch(user._id, {
          role: "guest",
        });
        fixedCount++;
      }
    }
    
    return { message: `Fixed ${fixedCount} users without roles` };
  },
});