/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as admin from "../admin.js";
import type * as auth from "../auth.js";
import type * as clientProfiles from "../clientProfiles.js";
import type * as dashboard from "../dashboard.js";
import type * as fixDatabase from "../fixDatabase.js";
import type * as migrations from "../migrations.js";
import type * as profiles from "../profiles.js";
import type * as uploadProfile from "../uploadProfile.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  admin: typeof admin;
  auth: typeof auth;
  clientProfiles: typeof clientProfiles;
  dashboard: typeof dashboard;
  fixDatabase: typeof fixDatabase;
  migrations: typeof migrations;
  profiles: typeof profiles;
  uploadProfile: typeof uploadProfile;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
