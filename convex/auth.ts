import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createUser = mutation({
  args: {
    email: v.string(),
    name: v.string(),
    company: v.optional(v.string()),
    role: v.optional(v.union(
      v.literal("admin"),
      v.literal("super_admin"),
      v.literal("dev"),
      v.literal("consultant"),
      v.literal("stakeholder"),
      v.literal("member"),
      v.literal("customer"),
      v.literal("guest")
    )),
    avatar: v.optional(v.string()),
    title: v.optional(v.string()),
    location: v.optional(v.string()),
    isVerified: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();

    if (existingUser) {
      return existingUser._id;
    }

    // Create new user
    const userId = await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
      company: args.company,
      role: args.role || "guest",
      avatar: args.avatar,
      title: args.title,
      location: args.location,
      isVerified: args.isVerified || false,
      createdAt: Date.now(),
    });

    return userId;
  },
});

export const createSession = mutation({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    // Find user first
    let user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();

    // If user doesn't exist, throw error instead of creating
    if (!user) {
      throw new Error("User not found. Please contact ALIAS for access.");
    }

    // Ensure user has a role
    if (!user.role) {
      await ctx.db.patch(user._id, {
        role: "guest",
      });
      user = await ctx.db.get(user._id);
    }

    if (!user) {
      throw new Error("Failed to create user session");
    }

    // Generate session token
    const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
    const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days

    await ctx.db.insert("sessions", {
      userId: user._id,
      token,
      expiresAt,
      createdAt: Date.now(),
    });

    return { token, user };
  },
});

export const createDevSession = mutation({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    // Find or create dev user
    let user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();

    if (!user) {
      const userId = await ctx.db.insert("users", {
        email: args.email,
        name: "Dev User",
        role: "dev",
        createdAt: Date.now(),
      });
      user = await ctx.db.get(userId);
    } else if (!user.role) {
      await ctx.db.patch(user._id, {
        role: "dev",
      });
      user = await ctx.db.get(user._id);
    }

    if (!user) {
      throw new Error("Failed to create dev session");
    }

    // Generate session token
    const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
    const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days

    await ctx.db.insert("sessions", {
      userId: user._id,
      token,
      expiresAt,
      createdAt: Date.now(),
    });

    return { token, user };
  },
});

export const validateSession = query({
  args: {
    token: v.string(),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db
      .query("sessions")
      .withIndex("by_token", (q) => q.eq("token", args.token))
      .unique();

    if (!session || session.expiresAt < Date.now()) {
      return null;
    }

    const user = await ctx.db.get(session.userId);
    return user;
  },
});

export const getUserByEmail = query({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();
  },
});

export const getUserByToken = query({
  args: {
    token: v.string(),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db
      .query("sessions")
      .withIndex("by_token", (q) => q.eq("token", args.token))
      .unique();

    if (!session || session.expiresAt < Date.now()) {
      return null;
    }

    return await ctx.db.get(session.userId);
  },
});

// Migration function to fix users without roles
export const fixUsersWithoutRoles = mutation({
  args: {},
  handler: async (ctx, args) => {
    const users = await ctx.db.query("users").collect();
    let fixedCount = 0;
    
    for (const user of users) {
      if (!user.role) {
        await ctx.db.patch(user._id, {
          role: "guest",
        });
        fixedCount++;
      }
    }
    
    return { message: `Fixed ${fixedCount} users without roles` };
  },
});