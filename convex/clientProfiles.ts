import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Enhanced profile structure to match the comprehensive JSON
export const uploadEnhancedProfile = mutation({
  args: {
    clientEmail: v.string(),
    profileData: v.object({
      company: v.object({
        name: v.string(),
        tagline: v.optional(v.string()),
        structure: v.optional(v.string()),
        established: v.optional(v.string()),
        location: v.optional(v.string()),
        certification: v.optional(v.string()),
      }),
      leadership: v.optional(v.object({
        founder: v.object({
          name: v.string(),
          title: v.string(),
          certifications: v.optional(v.array(v.string())),
          photo_url: v.optional(v.string()),
          journey: v.optional(v.record(v.string(), v.string())),
          philosophy: v.optional(v.array(v.string())),
          quote: v.optional(v.string()),
          mission_quote: v.optional(v.string()),
        }),
      })),
      mission: v.optional(v.object({
        statement: v.string(),
        attributed_to: v.optional(v.string()),
      })),
      core_values: v.optional(v.array(v.string())),
      services: v.optional(v.object({
        specialized: v.optional(v.array(v.object({
          name: v.string(),
          description: v.string(),
          category: v.optional(v.string()),
        }))),
        standard: v.optional(v.array(v.object({
          name: v.string(),
          description: v.string(),
          guarantee: v.optional(v.string()),
        }))),
        focus_areas: v.optional(v.array(v.string())),
      })),
      service_coverage: v.optional(v.object({
        primary_markets: v.optional(v.array(v.string())),
        extended_reach: v.optional(v.array(v.string())),
      })),
      key_metrics: v.record(v.string(), v.string()),
      target_markets: v.optional(v.object({
        high_value_segments: v.optional(v.array(v.object({
          name: v.string(),
          description: v.string(),
          category: v.optional(v.string()),
          revenue_indicator: v.optional(v.string()),
        }))),
        standard_segments: v.optional(v.array(v.object({
          name: v.string(),
          description: v.string(),
        }))),
        strategic_partnerships: v.optional(v.array(v.object({
          name: v.string(),
          description: v.string(),
          category: v.optional(v.string()),
        }))),
        revenue_mix: v.optional(v.record(v.string(), v.string())),
        growth_drivers: v.optional(v.array(v.string())),
        key_metrics: v.optional(v.record(v.string(), v.string())),
      })),
      digital_presence: v.optional(v.object({
        platforms: v.optional(v.array(v.object({
          name: v.string(),
          followers: v.optional(v.string()),
          views: v.optional(v.string()),
          engagement: v.optional(v.string()),
          content_focus: v.optional(v.string()),
          content_type: v.optional(v.string()),
          viral_potential: v.optional(v.string()),
          description: v.string(),
          url: v.optional(v.string()),
          design_quality: v.optional(v.string()),
          service_listings: v.optional(v.string()),
          testimonials: v.optional(v.string()),
        }))),
        content_strategy: v.optional(v.array(v.string())),
        growth_opportunities: v.optional(v.array(v.string())),
        review_sentiment: v.optional(v.object({
          positive: v.string(),
          praised_for: v.array(v.string()),
          improvement_areas: v.array(v.string()),
        })),
      })),
      competitive_landscape: v.optional(v.object({
        market_position: v.string(),
        competitors: v.optional(v.array(v.object({
          name: v.string(),
          position: v.string(),
          focus: v.string(),
          approach: v.string(),
          scale: v.optional(v.string()),
          pricing: v.optional(v.string()),
          guarantee: v.optional(v.string()),
          description: v.string(),
        }))),
        competitive_advantages: v.array(v.string()),
        market_positioning: v.optional(v.object({
          specialization_advantage: v.optional(v.string()),
          emotional_differentiation: v.optional(v.string()),
        })),
        strength_ratings: v.optional(v.record(v.string(), v.string())),
        strategic_insight: v.optional(v.string()),
      })),
      impact_and_recognition: v.optional(v.object({
        achievements: v.array(v.string()),
        partnerships: v.array(v.string()),
        vision_statement: v.optional(v.string()),
      })),
      operational_details: v.optional(v.object({
        culture: v.optional(v.string()),
        approach: v.optional(v.string()),
        project_scale: v.optional(v.string()),
        specializations: v.optional(v.array(v.string())),
      })),
      // AI Recommendations (ALIAS additions)
      ai_recommendations: v.optional(v.object({
        quick_wins: v.optional(v.array(v.object({
          title: v.string(),
          description: v.string(),
          impact: v.string(),
          timeframe: v.string(),
        }))),
        strategic_solutions: v.optional(v.array(v.object({
          title: v.string(),
          description: v.string(),
          benefits: v.array(v.string()),
          investment: v.string(),
        }))),
        next_steps: v.optional(v.array(v.string())),
        priority_score: v.optional(v.number()),
      })),
    }),
    clientInfo: v.optional(v.object({
      name: v.string(),
      title: v.optional(v.string()),
      location: v.optional(v.string()),
      avatar: v.optional(v.string()),
      isVerified: v.optional(v.boolean()),
    })),
  },
  handler: async (ctx, args) => {
    // Find or create user
    let user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.clientEmail))
      .unique();

    if (!user) {
      const userId = await ctx.db.insert("users", {
        email: args.clientEmail,
        name: args.clientInfo?.name || args.profileData.company.name,
        company: args.profileData.company.name,
        role: "stakeholder",
        title: args.clientInfo?.title,
        location: args.clientInfo?.location || args.profileData.company.location,
        avatar: args.clientInfo?.avatar,
        isVerified: args.clientInfo?.isVerified || false,
        createdAt: Date.now(),
      });
      user = await ctx.db.get(userId);
    } else {
      // Update user info if provided
      if (args.clientInfo) {
        await ctx.db.patch(user._id, {
          name: args.clientInfo.name || user.name,
          title: args.clientInfo.title || user.title,
          location: args.clientInfo.location || user.location,
          avatar: args.clientInfo.avatar || user.avatar,
          isVerified: args.clientInfo.isVerified ?? user.isVerified,
        });
      }
    }

    if (!user) throw new Error("Failed to create user");

    // Check if enhanced profile exists
    const existingProfile = await ctx.db
      .query("enhancedClientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", user._id))
      .unique();

    const profileData = {
      clientId: user._id,
      ...args.profileData,
      updatedAt: Date.now(),
    };

    if (existingProfile) {
      await ctx.db.patch(existingProfile._id, profileData);
      return { 
        success: true, 
        profileId: existingProfile._id, 
        action: "updated",
        message: `Enhanced profile updated for ${args.profileData.company.name}`
      };
    } else {
      const profileId = await ctx.db.insert("enhancedClientProfiles", {
        ...profileData,
        createdAt: Date.now(),
      });
      return { 
        success: true, 
        profileId, 
        action: "created",
        message: `Enhanced profile created for ${args.profileData.company.name}`
      };
    }
  },
});

// Get enhanced profile by token
export const getEnhancedProfileByToken = query({
  args: {
    token: v.string(),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db
      .query("sessions")
      .withIndex("by_token", (q) => q.eq("token", args.token))
      .unique();

    if (!session || session.expiresAt < Date.now()) {
      return null;
    }

    const profile = await ctx.db
      .query("enhancedClientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", session.userId))
      .unique();

    if (!profile) return null;

    const user = await ctx.db.get(session.userId);
    return {
      ...profile,
      client: user,
    };
  },
});

// Upload a client intelligence profile from JSON data (original format)
export const uploadProfile = mutation({
  args: {
    clientEmail: v.string(),
    profileData: v.object({
      companyName: v.string(),
      industry: v.string(),
      companySize: v.string(),
      website: v.optional(v.string()),
      businessDescription: v.string(),
      keyMetrics: v.array(v.object({
        label: v.string(),
        value: v.string(),
        trend: v.optional(v.string()),
      })),
      industryChallenges: v.array(v.string()),
      marketTrends: v.array(v.string()),
      competitiveAdvantages: v.array(v.string()),
      quickWins: v.array(v.object({
        title: v.string(),
        description: v.string(),
        impact: v.string(),
        timeframe: v.string(),
      })),
      strategicSolutions: v.array(v.object({
        title: v.string(),
        description: v.string(),
        benefits: v.array(v.string()),
        investment: v.string(),
      })),
      nextSteps: v.array(v.string()),
      priorityScore: v.number(),
    }),
    clientInfo: v.optional(v.object({
      name: v.string(),
      title: v.optional(v.string()),
      location: v.optional(v.string()),
      avatar: v.optional(v.string()),
      isVerified: v.optional(v.boolean()),
    })),
  },
  handler: async (ctx, args) => {
    // Find or create user
    let user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.clientEmail))
      .unique();

    if (!user) {
      const userId = await ctx.db.insert("users", {
        email: args.clientEmail,
        name: args.clientInfo?.name || args.profileData.companyName,
        company: args.profileData.companyName,
        role: "stakeholder",
        title: args.clientInfo?.title,
        location: args.clientInfo?.location,
        avatar: args.clientInfo?.avatar,
        isVerified: args.clientInfo?.isVerified || false,
        createdAt: Date.now(),
      });
      user = await ctx.db.get(userId);
    } else {
      // Update user info if provided
      if (args.clientInfo) {
        await ctx.db.patch(user._id, {
          name: args.clientInfo.name || user.name,
          title: args.clientInfo.title || user.title,
          location: args.clientInfo.location || user.location,
          avatar: args.clientInfo.avatar || user.avatar,
          isVerified: args.clientInfo.isVerified ?? user.isVerified,
        });
      }
    }

    if (!user) throw new Error("Failed to create user");

    // Check if profile exists
    const existingProfile = await ctx.db
      .query("clientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", user._id))
      .unique();

    const profileData = {
      clientId: user._id,
      ...args.profileData,
      updatedAt: Date.now(),
    };

    if (existingProfile) {
      await ctx.db.patch(existingProfile._id, profileData);
      return { 
        success: true, 
        profileId: existingProfile._id, 
        action: "updated",
        message: `Profile updated for ${args.profileData.companyName}`
      };
    } else {
      const profileId = await ctx.db.insert("clientProfiles", {
        ...profileData,
        createdAt: Date.now(),
      });
      return { 
        success: true, 
        profileId, 
        action: "created",
        message: `Profile created for ${args.profileData.companyName}`
      };
    }
  },
});

// Get all client profiles (for admin use)
export const getAllProfiles = query({
  args: {},
  handler: async (ctx, args) => {
    const profiles = await ctx.db
      .query("clientProfiles")
      .collect();

    // Get user info for each profile
    const profilesWithUsers = await Promise.all(
      profiles.map(async (profile) => {
        const user = await ctx.db.get(profile.clientId);
        return {
          ...profile,
          client: user,
        };
      })
    );

    return profilesWithUsers;
  },
});

// Delete a client profile
export const deleteProfile = mutation({
  args: {
    profileId: v.id("clientProfiles"),
  },
  handler: async (ctx, args) => {
    const profile = await ctx.db.get(args.profileId);
    if (!profile) {
      throw new Error("Profile not found");
    }

    await ctx.db.delete(args.profileId);
    return { success: true, message: "Profile deleted successfully" };
  },
});

// Get profile by client email
export const getProfileByEmail = query({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();

    if (!user) return null;

    const profile = await ctx.db
      .query("clientProfiles")
      .withIndex("by_client", (q) => q.eq("clientId", user._id))
      .unique();

    if (!profile) return null;

    return {
      ...profile,
      client: user,
    };
  },
});