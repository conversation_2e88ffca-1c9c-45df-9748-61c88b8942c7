import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    email: v.string(),
    name: v.string(),
    company: v.optional(v.string()),
    role: v.optional(v.union(
      v.literal("admin"),
      v.literal("super_admin"),
      v.literal("dev"),
      v.literal("consultant"),
      v.literal("stakeholder"),
      v.literal("member"),
      v.literal("customer"),
      v.literal("guest")
    )),
    avatar: v.optional(v.string()),
    title: v.optional(v.string()),
    location: v.optional(v.string()),
    isVerified: v.optional(v.boolean()),
    createdAt: v.number(),
  }).index("by_email", ["email"])
    .index("by_role", ["role"]),

  clientProfiles: defineTable({
    clientId: v.id("users"),
    companyName: v.string(),
    industry: v.string(),
    companySize: v.string(),
    website: v.optional(v.string()),
    
    // Business Overview
    businessDescription: v.string(),
    keyMetrics: v.array(v.object({
      label: v.string(),
      value: v.string(),
      trend: v.optional(v.string()),
    })),
    
    // Industry Analysis
    industryChallenges: v.array(v.string()),
    marketTrends: v.array(v.string()),
    competitiveAdvantages: v.array(v.string()),
    
    // AI Opportunities
    quickWins: v.array(v.object({
      title: v.string(),
      description: v.string(),
      impact: v.string(),
      timeframe: v.string(),
    })),
    
    strategicSolutions: v.array(v.object({
      title: v.string(),
      description: v.string(),
      benefits: v.array(v.string()),
      investment: v.string(),
    })),
    
    // Recommendations
    nextSteps: v.array(v.string()),
    priorityScore: v.number(),
    
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_client", ["clientId"]),

  // Enhanced client profiles for comprehensive data
  enhancedClientProfiles: defineTable({
    clientId: v.id("users"),
    
    // Company Information
    company: v.object({
      name: v.string(),
      tagline: v.optional(v.string()),
      structure: v.optional(v.string()),
      established: v.optional(v.string()),
      location: v.optional(v.string()),
      certification: v.optional(v.string()),
    }),
    
    // Leadership
    leadership: v.optional(v.object({
      founder: v.object({
        name: v.string(),
        title: v.string(),
        certifications: v.optional(v.array(v.string())),
        photo_url: v.optional(v.string()),
        journey: v.optional(v.record(v.string(), v.string())),
        philosophy: v.optional(v.array(v.string())),
        quote: v.optional(v.string()),
        mission_quote: v.optional(v.string()),
      }),
    })),
    
    // Mission & Values
    mission: v.optional(v.object({
      statement: v.string(),
      attributed_to: v.optional(v.string()),
    })),
    core_values: v.optional(v.array(v.string())),
    
    // Services
    services: v.optional(v.object({
      specialized: v.optional(v.array(v.object({
        name: v.string(),
        description: v.string(),
        category: v.optional(v.string()),
      }))),
      standard: v.optional(v.array(v.object({
        name: v.string(),
        description: v.string(),
        guarantee: v.optional(v.string()),
      }))),
      focus_areas: v.optional(v.array(v.string())),
    })),

    
    // Coverage
    service_coverage: v.optional(v.object({
      primary_markets: v.optional(v.array(v.string())),
      extended_reach: v.optional(v.array(v.string())),
    })),
    
    // Metrics
    key_metrics: v.record(v.string(), v.string()),
    
    // Markets
    target_markets: v.optional(v.object({
      high_value_segments: v.optional(v.array(v.object({
        name: v.string(),
        description: v.string(),
        category: v.optional(v.string()),
        revenue_indicator: v.optional(v.string()),
      }))),
      standard_segments: v.optional(v.array(v.object({
        name: v.string(),
        description: v.string(),
      }))),
      strategic_partnerships: v.optional(v.array(v.object({
        name: v.string(),
        description: v.string(),
        category: v.optional(v.string()),
      }))),
      revenue_mix: v.optional(v.record(v.string(), v.string())),
      growth_drivers: v.optional(v.array(v.string())),
      key_metrics: v.optional(v.record(v.string(), v.string())),
    })),
    
    // Digital Presence
    digital_presence: v.optional(v.object({
      platforms: v.optional(v.array(v.object({
        name: v.string(),
        followers: v.optional(v.string()),
        views: v.optional(v.string()),
        engagement: v.optional(v.string()),
        content_focus: v.optional(v.string()),
        content_type: v.optional(v.string()),
        viral_potential: v.optional(v.string()),
        description: v.string(),
        url: v.optional(v.string()),
        design_quality: v.optional(v.string()),
        service_listings: v.optional(v.string()),
        testimonials: v.optional(v.string()),
      }))),
      content_strategy: v.optional(v.array(v.string())),
      growth_opportunities: v.optional(v.array(v.string())),
      review_sentiment: v.optional(v.object({
        positive: v.string(),
        praised_for: v.array(v.string()),
        improvement_areas: v.array(v.string()),
      })),
    })),
    
    // Competition
    competitive_landscape: v.optional(v.object({
      market_position: v.string(),
      competitors: v.optional(v.array(v.object({
        name: v.string(),
        position: v.string(),
        focus: v.string(),
        approach: v.string(),
        scale: v.optional(v.string()),
        pricing: v.optional(v.string()),
        guarantee: v.optional(v.string()),
        description: v.string(),
      }))),
      competitive_advantages: v.array(v.string()),
      market_positioning: v.optional(v.object({
        specialization_advantage: v.optional(v.string()),
        emotional_differentiation: v.optional(v.string()),
      })),
      strength_ratings: v.optional(v.record(v.string(), v.string())),
      strategic_insight: v.optional(v.string()),
    })),
    
    // Recognition
    impact_and_recognition: v.optional(v.object({
      achievements: v.array(v.string()),
      partnerships: v.array(v.string()),
      vision_statement: v.optional(v.string()),
    })),
    
    // Operations
    operational_details: v.optional(v.object({
      culture: v.optional(v.string()),
      approach: v.optional(v.string()),
      project_scale: v.optional(v.string()),
      specializations: v.optional(v.array(v.string())),
    })),
    
    // AI Recommendations
    ai_recommendations: v.optional(v.object({
      quick_wins: v.optional(v.array(v.object({
        title: v.string(),
        description: v.string(),
        impact: v.string(),
        timeframe: v.string(),
      }))),
      strategic_solutions: v.optional(v.array(v.object({
        title: v.string(),
        description: v.string(),
        benefits: v.array(v.string()),
        investment: v.string(),
      }))),
      next_steps: v.optional(v.array(v.string())),
      priority_score: v.optional(v.number()),
    })),
    
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_client", ["clientId"]),

  sessions: defineTable({
    userId: v.id("users"),
    token: v.string(),
    expiresAt: v.number(),
    createdAt: v.number(),
  }).index("by_token", ["token"])
    .index("by_user", ["userId"]),

  projects: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    clientId: v.id("users"),
    status: v.union(
      v.literal("planning"),
      v.literal("active"),
      v.literal("on_hold"),
      v.literal("completed"),
      v.literal("cancelled")
    ),
    priority: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent")
    ),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    budget: v.optional(v.string()),
    teamMembers: v.array(v.id("users")),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_client", ["clientId"])
    .index("by_status", ["status"]),

  collections: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    icon: v.string(),
    color: v.optional(v.string()),
    ownerId: v.id("users"),
    isStarred: v.optional(v.boolean()),
    memberCount: v.optional(v.number()),
    lastActivity: v.optional(v.number()),
    createdAt: v.number(),
  }).index("by_owner", ["ownerId"])
    .index("by_starred", ["isStarred"]),

  groups: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    memberCount: v.number(),
    lastActivity: v.optional(v.number()),
    isStarred: v.optional(v.boolean()),
    ownerId: v.id("users"),
    createdAt: v.number(),
  }).index("by_owner", ["ownerId"])
    .index("by_starred", ["isStarred"]),
});